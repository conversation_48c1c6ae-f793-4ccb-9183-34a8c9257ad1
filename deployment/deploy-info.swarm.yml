bo-gateway:
  version: "${GO_PIPELINE_LABEL}"
  image: "${BO_GATEWAY_DOCKER_IMAGE}"
  dns_name: bo-gateway 
  port: 3000
  replicas: ${BO_GATEWAY_REPLICAS}
  memory_limit: "${BO_GATEWAY_MEMORY_LIMIT}"
  environment_variables:
    - AUDIT_SERVICE_HOST=${BO_GATEWAY_ENV_AUDIT_SERVICE_HOST}
    - AUDIT_SERVICE_PORT=${BO_GATEWAY_ENV_AUDIT_SERVICE_PORT}
    - CUSTOMER_SERVICE_HOST=${BO_GATEWAY_ENV_CUSTOMER_SERVICE_HOST}
    - CUSTOMER_SERVICE_PORT=${BO_GATEWAY_ENV_CUSTOMER_SERVICE_PORT}
    - DATADOG_API_KEY=${BO_GATEWAY_ENV_DATADOG_API_KEY}
    - DATADOG_ENABLED=${BO_GATEWAY_ENV_DATADOG_ENABLED}
    - ELASTICSEARCH_URI=${BO_GATEWAY_ENV_ELASTICSEARCH_URL}
    - FEED_SERVICE_HOST=${BO_GATEWAY_ENV_FEED_SERVICE_HOST}
    - FEED_SERVICE_PORT=${BO_GATEWAY_ENV_FEED_SERVICE_PORT}
    - KEYCLOAK_REALM=${BO_GATEWAY_BO_KEYCLOAK_REALM}
    - KEYCLOAK_URL=${BO_GATEWAY_KEYCLOAK_URL}
    - KYC_GATEWAY_HOST=${BO_GATEWAY_ENV_KYC_GATEWAY_HOST}
    - KYC_GATEWAY_PORT=${BO_GATEWAY_ENV_KYC_GATEWAY_PORT}
    - METRICS_ENV=${BO_GATEWAY_ENV_METRICS_ENV}
    - PAYMENT_SERVICE_HOST=${BO_GATEWAY_ENV_PAYMENT_SERVICE_HOST}
    - PAYMENT_SERVICE_PORT=${BO_GATEWAY_ENV_PAYMENT_SERVICE_PORT}
    - TRADING_ACCOUNT_SERVICE_HOST=${BO_GATEWAY_ENV_TRADING_ACCOUNT_SERVICE_HOST}
    - TRADING_ACCOUNT_SERVICE_PORT=${BO_GATEWAY_ENV_TRADING_ACCOUNT_SERVICE_PORT}
    - REPORTING_SERVICE_HOST=${BO_GATEWAY_ENV_REPORTING_SERVICE_HOST}
    - REPORTING_SERVICE_PORT=${BO_GATEWAY_ENV_REPORTING_SERVICE_PORT}
    - DEFAULT_CONNECTION_TIMEOUT=${BO_DEFAULT_CONNECTION_TIMEOUT}
    - EXTENDED_SEARCH_TIMEOUT=${BO_EXTENDED_SEARCH_TIMEOUT}
