worker_processes  1;

events {
    worker_connections  1024;
    use epoll;
}

http {
    upstream host_service {
       server host.docker.internal:3000;
    }

    server {
        listen 80;
        listen [::]:80;

        server_name local.skilling.com;

        location /bog/ {
            proxy_pass http://host_service/;

            proxy_set_header    Host                $http_host;
            proxy_set_header    X-Real-IP           $realip_remote_addr;
            proxy_set_header    X-Forwarded-Proto   $scheme;
            proxy_set_header    X-Forwarded-For     $proxy_add_x_forwarded_for;
        }
    }
}