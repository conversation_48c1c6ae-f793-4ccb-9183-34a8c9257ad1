plugins {
    id 'io.spring.dependency-management' version '1.1.7'
    id 'org.springframework.boot' version '3.4.1'
    id 'java'
    id 'jacoco'
}

apply from: 'test.gradle'

group = 'com.propero'
version = System.getenv('VERSION') ?: 'local'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

configurations {
  compileOnly {
    extendsFrom annotationProcessor
  }
}

repositories {
    mavenCentral()
    mavenLocal()
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        mavenBom("io.opentelemetry.instrumentation:opentelemetry-instrumentation-bom:${otelVersion}")
    }
}

dependencies {
    implementation platform("org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}")

    compileOnly 'org.projectlombok:lombok'
    implementation "com.google.guava:guava:${guavaVersion}"
    implementation 'org.apache.commons:commons-lang3'

    implementation 'io.opentelemetry.instrumentation:opentelemetry-spring-boot-starter'
    implementation 'io.opentelemetry:opentelemetry-exporter-otlp'
    implementation 'io.micrometer:micrometer-tracing-bridge-otel'
    implementation 'net.logstash.logback:logstash-logback-encoder:7.3'

    // spring
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.cloud:spring-cloud-starter-gateway'
    implementation 'org.springframework.cloud:spring-cloud-gateway-webflux'
    implementation 'org.springframework.retry:spring-retry'

    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-client'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.springframework.session:spring-session-data-redis'

    implementation "org.springdoc:springdoc-openapi-starter-webflux-ui:${springdocVersion}"

    implementation 'com.jayway.jsonpath:json-path:2.9.0'

    annotationProcessor("org.projectlombok:lombok")
}

defaultTasks 'clean', 'classes', 'testClasses', 'check', 'assemble'

springBoot {
    buildInfo()
}

bootJar {
    archiveFileName = 'bo-gateway.jar'
}
