#!/usr/bin/env bash
# vim: et sr sw=4 ts=4 smartindent:
#
# run jar on startup, or if requested run with datadog tracing java agent
# (Can be requested by setting env var DATADOG_TRACING to "true")
#
DATADOG_TRACER_JAR="/var/tmp/dd-java-agent.jar"
DATADOG_TRACER_JAR_URL="https://dtdg.co/latest-java-tracer"

TRACING_ENVVARS="
    DD_ENV                  `# set in ECS task def`
    DD_SERVICE              `# set in ECS task def`
    DD_VERSION              `# set in ECS task def`
    DD_TRACE_SAMPLE_RATE    `# set in ECS task def or defaults to 0.1`
"

err() { echo "{\"time\":\"$(date '+%FT%T.%3NZ')\",\"msg\":\"$1\",\"level_desc\":\"ERROR\"}"; }

get_host_ip() {
    local ip=''
    if [[ -r "$ECS_CONTAINER_METADATA_FILE" ]]; then
        ip="$(cat "$ECS_CONTAINER_METADATA_FILE" | jq -r .HostPrivateIPv4Address)"
    elif ip=$(curl -sSf http://***************/latest/meta-data/local-ipv4)
    then
        :
    else
        err "$0: couldn't determine host IP"
        return 1
    fi

    if ! [[ "$ip" =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        err "$0: couldn't get valid host IPv4 address. Got [$ip]"
        return 1
    fi

    echo "$ip"
}

empty_string() { [[ -z "${!1}" ]] ; }
required_vars() {
    local rc=0
    local required_vars="$1"
    local this_var=""
    for this_var in $required_vars; do
        if empty_string "$this_var"
        then
            failed="${failed:+$failed }\$$this_var"
            rc=1
        fi
    done
    [[ $rc -ne 0 ]] && err "ERROR following vars must be non-empty in env: $failed"
    return $rc
}

has_required_tools() {
    local tools="$1"
    local rc=0
    for tool in $tools; do
        if ! command -v $tool &>/dev/null
        then
            err "$0: $tool not found - required to enable tracing"
            rc=1
        fi
    done
    return $rc
}

get_tracer_jar() {
    curl -sSL --retry 3 --retry-delay 5 \
        -o "$DATADOG_TRACER_JAR" \
        "$DATADOG_TRACER_JAR_URL"

    if [[ ! -r "$DATADOG_TRACER_JAR" ]]; then
        err "$0: couldn't fetch tracer jar."
        return 1
    fi

    return 0
}

tracing_requested() { [[ "${DATADOG_TRACING,,}" =~ ^(true|yes|y)$ ]]; }

tracing_setup() {
    # $DD_TRACE_SAMPLE_RATE
    # default to 10% of requests sampled to reduce overload of data
    [[ -z "$DD_TRACE_SAMPLE_RATE" ]] && export DD_TRACE_SAMPLE_RATE=0.1
    if ! [[ "$DD_TRACE_SAMPLE_RATE" =~ ^(0\.[0-9]+|1(\.0+)?)$ ]]; then
        err "DD_TRACE_SAMPLE_RATE value must be > 0.0 and <= 1.0 - got [$DD_TRACE_SAMPLE_RATE]"
        return 1
    fi

    local ip=''
    required_vars "$TRACING_ENVVARS" || return 1
    has_required_tools "jq" "curl" || return 1
    get_tracer_jar || return 1

    # env vars required for tracing
    ip="$(get_host_ip)" || return 1
    CONTAINER_HOST_IP="$ip"
    DD_AGENT_HOST="$ip"
    export CONTAINER_HOST_IP DD_AGENT_HOST

}

__cmd_args=("$@") # default to normal entrypoint
if tracing_requested
then
    if tracing_setup
    then
        __cmd_args=("java" "-javaagent:${DATADOG_TRACER_JAR}" "-jar" "${@: -1}")
    else
        err "$0: couldn't set up datadog tracing correctly. Will not enable it."
    fi
fi

exec "${__cmd_args[@]}"
