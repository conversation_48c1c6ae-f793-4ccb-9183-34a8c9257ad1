# Keycloak Configuration
This guide explains how to configure all components needed for the integration with Keycloak.  
For convenience we will show how to configure it in a dockerized instance, but all the steps applies to other environments too.  
We will assume that Keycloak is up and running (see [local run](../README.md#local-run))

## Realm
First of all we have to create a Realm (in Staging and Prod this needs to be done by devops).

Once logged in the admin console expand the burger menu, click on the dropdown list and on "Create realm".
Define the realm name (in our case "local"), make sure realm is enabled and click on "Create".
![](./images/create_realm.png)  

## Identity Provider
To configure an identity provider we have to click on the "Identity providers" menu, 
then click on Google, set `clientId` and `secret` provided by devops and click on "Add".
In the next page (provider details) scroll down to the advanced settings section and make sure `first broker login` is selected as "First login flow".  
  
```
NOTE:
We are using the standard Authentication flow here, if you want to customize it, 
for exapmple to remove username/password inputs,
create a custom flow under the `Authentication` menu and make sure to assign it to the Provider.
```

## Client
To create a new client go to `Clients` menu and click on "Create client", then define Client ID (bo-local) and optionally
Name and Description, then click on Next.  
In the Capability Config toggle on the `Client authentication` and tick only `Standard flow` in the Authentication flow section
![](./images/client_config.png) 
  
Then click on "Next" and define `Valid redirect URIs` and `Web origins`
![](./images/client_redirect_uris.png) 
Click on "Save".

As the `Client authentication` has been activated in a previous step, we can see the `Credentials` tab, which contains 
the `Client Secret` we will have to set in the `KEYCLOAK_CLIENT_SECRET` env variable.
![](./images/client_secret.png) 

### Client scopes
In the detail page of a client we can also access the `Client scopes` tab,
if we click on it and then on the default client scope (i.e. `bo-local-dedicated`) we will have the option to create new mappers.  
In the case of backoffice application, we need to have access to the groups (and roles) assigned to a user 
and to do so we are setting a cookie in `bo-gateway`, which requires mappers for:
- user groups
- user roles
---

#### Add mappings

*Groups*  
Click on `Configure a new mapper`, select `Group Membership` and define a name and `Token Claim Name`.
The value of `Token Claim Name` must be `permissions.groups`.
We can leave the other options active as per default and click on "Save".

![](./images/client_groups_mapping.png)


*Roles*  
Click on `Add mapper`, select `By configuration` and then `User Realm Role` and define a name and  `Token Claim Name`.
The value of `Token Claim Name` must be `permissions.roles`.
We can leave the other options active as per default and click on "Save".

![](./images/client_roles_mapping.png)

---

### Realm roles
On the `Realm roles` section we have to define a set of roles that will be assigned to groups.
The full list of custom roles is:
- create-account
- cysec-access
- delete-kyc
- edit-comment-compliance
- edit-comment-support
- edit-customer
- edit-kyc
- edit-transactions
- reset-password
- seychelles-access
- verify-account
- view-customer
- view-kyc
- view-transactions

This list basically contains all the [AccessPermissions](../src/main/java/com/propero/bogateway/config/AccessPermissions.java)
plus `cysec-access` and `seychelles-access` which are used for data filtering by the BO application.

### Groups
On the `Groups` section we have to define a set of groups to which we have to add the roles defined before.  
Once created a group, to add the roles we have to go to the `Role mapping` tab, click on "Assign role", select the roles we want to assign and click on "Assign".

The full list of custom groups is:

| Group            | Assigned roles                                                                                                                                                                                                                                          |
|------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| backoffice       | create-account <br/> edit-comment-support <br/> edit-customer <br/> edit-kyc <br/> edit-transactions <br/> reset-password <br/> verify-account <br/> view-customer <br/> view-kyc <br/> view-transactions                                               |
| compliance       | edit-comment-compliance <br/> edit-customer <br/> edit-kyc <br/> verify-account <br/> view-customer <br/> view-kyc <br/> view-transactions                                                                                                              |
| customer-support | create-account<br/> edit-comment-support <br/> edit-customer <br/> reset-password <br/> view-customer <br/> view-kyc <br/> view-transactions                                                                                                            |
| cysec            | cysec-access                                                                                                                                                                                                                                            |
| finance          | edit-transactions <br/> view-customer <br/> view-kyc <br/> view-transactions                                                                                                                                                                            |
| read-only        | view-customer <br/> view-kyc <br/> view-transactions                                                                                                                                                                                                    |
| seychelles       | seychelles-access                                                                                                                                                                                                                                       |
| superuser        | create-account <br/> delete-kyc <br/> edit-comment-compliance <br/>edit-comment-support <br/> edit-customer <br/> edit-kyc <br/> edit-transactions <br/> reset-password <br/> verify-account <br/> view-customer <br/> view-kyc <br/> view-transactions |
| trader           | view-customer <br/> view-kyc <br/> view-transactions                                                                                                                                                                                                    |

#### Assign groups
To assign groups to a user we have to go to the `User` section, search the user, click on it to access its details, then move to the `Groups` tab and click on "Join Group".
Here we can select all the groups to assign to a user.

```
NOTE:
"cysec" and "seychelles" are special groups which are used by BO to filter data by license,
so a BO user will be assigned to at least 2 grups, the "department" one (backoffice, trader, finance, etc)
and cysec, seychelles (or both of them) to properly filter the data that can be accessed.

```