test {
    useJUnitPlatform()
    finalizedBy jacocoTestReport
    testLogging {
        events = ['passed', 'skipped', 'failed']
        showExceptions = true
        exceptionFormat = 'full'
        showCauses = true
        showStackTraces = true
    }
}

jacocoTestReport {
    dependsOn test
    reports {
        xml.required = false
        csv.required = false
        html.required = true
    }
}

jacocoTestCoverageVerification {
    violationRules {
        rule {
            limit {
                minimum = 0.6
            }
        }
    }
}

dependencyManagement {
    imports {
        mavenBom "org.testcontainers:testcontainers-bom:${testcontainersVersion}"
    }
}

dependencies {
    testImplementation group: 'org.wiremock',                               name: 'wiremock-standalone',                version: "${wiremockVersion}"
    testImplementation group: 'org.wiremock.integrations.testcontainers',   name: 'wiremock-testcontainers-module',     version: "${wiremockTestcontainersVersion}"
    testImplementation group: 'com.redis',                                  name: 'testcontainers-redis',               version: "${redisTestContainersVersion}"

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation 'io.projectreactor:reactor-test'
    testImplementation "com.c4-soft.springaddons:spring-addons-oauth2-test:${springAddonsTestVersion}"

    testCompileOnly("org.projectlombok:lombok")
    testAnnotationProcessor("org.projectlombok:lombok")
}