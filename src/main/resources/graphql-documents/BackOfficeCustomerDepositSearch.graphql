query BackOfficeCustomerDepositSearch(
    $depositCondition: oag_deposit_completed_bool_exp!
    $customerCondition: oag_users_bool_exp!
    $pageSize: Int = 25
    $pageOffset: Int = 0
) {
    warehouse_db {

        stats: oag_users_aggregate(  where: {_and:[
            $customerCondition
            {deposit_completed: {deposit_count: {_eq: 1}}}
            {deposit_completed: $depositCondition }
        ]}) {
            aggregate{
                count
            }
        }

        customers: oag_users(
            limit: $pageSize
            offset: $pageOffset
            where: {_and:[
                $customerCondition
                {deposit_completed: {deposit_count: {_eq: 1}}}
                {deposit_completed: $depositCondition }
            ]}) {
            customer {
                id
                legalEntity: legal_entity
                email
                brand
                flagged
                registrationMethod: registration_method
                onboardingStatus: onboarding_status
                currency: registered_currency
                created: created_at
                kycSummary: kyc_summary
                fullName: person {
                    firstName: first_name
                    lastName: last_name
                }
                deposits: financial_statements_aggregate(where: {status: {_eq: "FINISHED"}, operation_type:{_eq: "DEPOSIT"}}) {
                    aggregate {
                        sum {
                            amount: eur_amount_delta
                        }
                    }
                }
            }
        }
    }
}
