query BackOfficeTransactionsByCustomer(
    $size:Int!
    $offset:Int!
    $customerCondition: customer_bool_exp!
    $transactionCondition: financial_transaction_bool_exp!
    $sortBy: order_by!
){
    results: customer_db {
        customers: customer(where:$customerCondition) {
            transactions: financial_statements(
                where: $transactionCondition
                order_by: {transaction_date: $sortBy}
                offset: $offset
                limit: $size
            ){
                customer {
                    customerId: id
                    email
                    person {
                        firstName: first_name
                        lastName: last_name
                    }
                }

                transactionId: ft_id
                internalTransactionId: internal_transaction_id
                createdAt: transaction_date
                updatedAt: updated_at

                tradingAccountId: trading_account_id
                delta
                transactionType: transaction_type
                license
                provider
                providerTransactionType: provider_transaction_type
                paymentMethod: payment_method
                operationType: operation_type
                currency
                description
                externalOperationId: external_operation_id
                status
                payment: transaction {
                    destinationType: destination_type
                    paymentProcessor: payment_processor
                    comment
                }
                amount: delta
                amountEur: eur_amount_delta
            }
        }
    }
}
