query BackOfficeCustomerTradingAccountSearch(
    $tradingAccountCondition: accounts_bool_exp! = {}
    $pageSize: Int = 25
    $pageOffset: Int = 0
) {
    customer_db {

        stats: accounts_aggregate(where: $tradingAccountCondition){
            aggregate {
                count
            }
        }

        accounts: accounts(where: $tradingAccountCondition offset: $pageOffset limit: $pageSize) {
            customer {
                id
                legalEntity: legal_entity
                email
                brand
                flagged
                registrationMethod: registration_method
                onboardingStatus: onboarding_status
                currency: registered_currency
                created: created_at
                kycSummary: kyc_summary
                fullName: person {
                    firstName: first_name
                    lastName: last_name
                }

                deposits: financial_statements_aggregate(where: { _and: [
                    { status: {_eq: "FINISHED"} }
                    { operation_type:{_eq: "DEPOSIT"}}
                ]}) {
                    aggregate {
                        sum {
                            amount: eur_amount_delta
                        }
                    }
                }
            }
        }
    }
}
