query BackOfficeCustomerSearch(
    $customerCondition: customer_bool_exp! = {}
    $pageSize: Int = 25
    $pageOffset: Int = 0
) {
    customer_db {

        stats: customer_aggregate(where: $customerCondition){
            aggregate{
                count
            }
        }

        customers: customer(where: $customerCondition offset:$pageOffset limit:$pageSize) {
            id
            legalEntity: legal_entity
            email
            brand
            flagged
            registrationMethod: registration_method
            onboardingStatus: onboarding_status
            currency: registered_currency
            created: created_at
            kycSummary: kyc_summary
            fullName: person {
                firstName: first_name
                lastName: last_name
            }

            deposits: financial_statements_aggregate(where: { _and: [
                { status: {_eq: "FINISHED"} }
                { operation_type:{_eq: "DEPOSIT"}}
            ]}) {
                aggregate {
                    sum {
                        amount: eur_amount_delta
                    }
                }
            }
        }
    }
}
