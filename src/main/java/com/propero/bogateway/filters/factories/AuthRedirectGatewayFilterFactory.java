package com.propero.bogateway.filters.factories;

import com.propero.bogateway.config.GatewayProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.GatewayFilterFactory;
import org.springframework.cloud.gateway.filter.factory.RedirectToGatewayFilterFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.Optional;

import static com.propero.bogateway.filters.factories.AddUserDetailsFilterFactory.BO_USER_DETAILS;
import static java.util.function.Predicate.not;

/**
 * Factory for a {@link GatewayFilter} that redirects to
 * the value of the {@code redirect_uri} parameter on the inbound request. <br>
 * It also catches user_details from the exchange context and set it as query param. <br>
 * The filter delegates to a standard {@link RedirectToGatewayFilterFactory} to apply
 * the redirect to the response.
 *
 * @see RedirectToGatewayFilterFactory
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AuthRedirectGatewayFilterFactory implements GatewayFilterFactory<AuthRedirectGatewayFilterFactory.Config> {

    private final GatewayProperties gatewayProperties;
    private final RedirectToGatewayFilterFactory redirectToGatewayFilterFactory;

    private static final String PARAM_KEY_REDIRECT_URI = "redirect_uri";

    public GatewayFilter apply() {
        return apply(new Config());
    }

    @Override
    public GatewayFilter apply(Config config) {
        return ((exchange, chain) -> {
            String redirectPath = getRedirectPath(exchange.getRequest());
            URI redirectUri = buildRedirectUri(redirectPath, exchange);
            return redirectToGatewayFilterFactory.apply(HttpStatus.FOUND, redirectUri)
                    .filter(exchange, chain);
        });
    }

    private String getRedirectPath(ServerHttpRequest request) {
        return Optional.ofNullable(request.getQueryParams().get(PARAM_KEY_REDIRECT_URI))
                .flatMap(values -> values.stream()
                        .filter(not(String::isBlank))
                        .findFirst())
                .orElseThrow(() -> {
                    log.error("Failed to construct redirect URI - {} param was null or empty", PARAM_KEY_REDIRECT_URI);
                    return new ResponseStatusException(HttpStatus.BAD_REQUEST);
                });
    }

    private URI buildRedirectUri(String redirectPath, ServerWebExchange exchange) {
        return UriComponentsBuilder.fromUriString(gatewayProperties.getBaseUri())
                .path(redirectPath)
                .queryParam(BO_USER_DETAILS, Optional.ofNullable(exchange.getAttribute(BO_USER_DETAILS)))
                .build()
                .toUri();
    }

    public static class Config {
    }
}
