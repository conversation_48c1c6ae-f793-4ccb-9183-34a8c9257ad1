package com.propero.bogateway.filters.factories;

import lombok.Builder;
import lombok.Data;
import lombok.Singular;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.client.authentication.OAuth2AuthenticationToken;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Mono;

import java.util.Collections;
import java.util.Optional;
import java.util.Set;

@Component
public class AuthFilterGatewayFactory extends AbstractGatewayFilterFactory<AuthFilterGatewayFactory.Config> {

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> exchange.getPrincipal()
                .map(principal -> principal)
                .filter(principal -> principal instanceof OAuth2AuthenticationToken)
                .switchIfEmpty(Mono.error(new InsufficientAuthenticationException("User not authenticated")))
                .filter(authentication -> isAllowed((OAuth2AuthenticationToken) authentication, config.getRoles()))
                .switchIfEmpty(Mono.error(new ResponseStatusException(HttpStatus.FORBIDDEN)))
                .then(chain.filter(exchange));
    }

    protected boolean isAllowed(OAuth2AuthenticationToken authentication, Set<String> whitelistedRoles) {
        return Optional.ofNullable(authentication.getAuthorities())
                .orElseGet(Collections::emptySet)
                .stream()
                .map(GrantedAuthority::getAuthority)
                .anyMatch(whitelistedRoles::contains);
    }

    @Builder
    @Data
    public static class Config {

        @Singular
        private final Set<String> roles;

        public static Config withRoles(String... whitelistedRoles) {
            return new Config(Set.of(whitelistedRoles));
        }
    }
}
