package com.propero.bogateway.filters.factories;

import com.google.common.collect.ImmutableList;
import com.propero.bogateway.elasticsearch.AccountType;
import com.propero.bogateway.elasticsearch.IndexRange;
import com.propero.bogateway.elasticsearch.QueryParams;
import com.propero.bogateway.helpers.ElasticsearchHelper;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Delegate;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Stream;

import static com.google.common.collect.ImmutableMap.of;
import static java.time.ZoneOffset.UTC;
import static java.util.stream.Collectors.joining;

@RequiredArgsConstructor
@Component
public class CustomerActivityGatewayFilterFactory extends AbstractGatewayFilterFactory<CustomerActivityGatewayFilterFactory.Config> {

    @Delegate
    private final ElasticsearchHelper elasticsearchHelper;

    @Override
    public GatewayFilter apply(Config config) {
        return new GatewayFilter() {
            @Override
            public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
                return config.getQueryParamsBuilder().apply(exchange).flatMap(queryParams -> {
                    IndexRange indexRange = new IndexRange(toLocalDate(queryParams.getFrom()), toLocalDate(queryParams.getTo()));
                    return chain.filter(elasticsearchHelper.rebuildAsSearchRequest(
                            exchange,
                            elasticsearchHelper.serializeSearchRequestBody(buildQuery(queryParams)),
                            indexRangeToQuery(indexRange)));
                });
            }

            private Map<String, Object> buildQuery(QueryParams queryParams) {
                Map<String, Object> q = new HashMap<>();
                q.put("size", queryParams.getSize());
                q.put("from", queryParams.getOffset());
                q.put("sort", of("timestamp", "desc"));
                q.put("query", of("bool", toMap("must", getMustFilters(queryParams))));

                return q;
            }

            private List<Object> getMustFilters(QueryParams queryParams) {
                ImmutableList.Builder<Object> filters = ImmutableList.builder().add(
                        of("term", of("type", of("value", queryParams.getDocumentType()))),
                        of("term", of("customerId", of("value", queryParams.getCustomerId()))),
                        of("range", of("timestamp", of("gte", queryParams.getFrom(), "lt", queryParams.getTo()))));
                return filters.build();
            }

            private Map<Object, Object> toMap(Object... items) {
                Assert.isTrue(items.length % 2 == 0, "Odd number of params provided");
                Map<Object, Object> map = new HashMap<>();
                for (int i = 0; i < items.length; i += 2) {
                    map.put(items[i], items[i + 1]);
                }
                return map;
            }
        };
    }

    @Getter
    @RequiredArgsConstructor(staticName = "from")
    public static class Config {
        private final Function<ServerWebExchange, Mono<QueryParams>> queryParamsBuilder;
    }

    private static LocalDate toLocalDate(Long millis) {
        return Instant.ofEpochMilli(millis).atOffset(UTC).toLocalDate();
    }

    private static String indexRangeToQuery(IndexRange indexRange) {
        return Stream.of(AccountType.values())
                .map(accType -> indexRange.getIndexPattern(accType, "statements"))
                .collect(joining(","));
    }
}
