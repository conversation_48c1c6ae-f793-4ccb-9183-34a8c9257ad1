package com.propero.bogateway.filters.factories;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.GatewayFilterFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.oauth2.client.authentication.OAuth2AuthenticationToken;
import org.springframework.security.oauth2.core.oidc.user.DefaultOidcUser;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;

@RequiredArgsConstructor
@Configuration
@Slf4j
public class AddUserDetailsFilterFactory implements GatewayFilterFactory<AddUserDetailsFilterFactory.Config>   {

    public static final String BO_USER_DETAILS = "user_details";

    private static final ObjectMapper mapper = new ObjectMapper();

    @Bean
    public GatewayFilter apply() {
        return apply(new Config());
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) ->
            exchange.getPrincipal()
                .filter(principal -> principal instanceof OAuth2AuthenticationToken)
                .switchIfEmpty(Mono.error(new InsufficientAuthenticationException("User not authenticated")))
                .<ServerWebExchange>handle((principal, sink) -> {
                    OAuth2AuthenticationToken token = (OAuth2AuthenticationToken) principal;
                    OAuth2User user = token.getPrincipal();
                    Map<String, Object> claims = ((DefaultOidcUser) user).getClaims();
                    UserDetails userDetails = buildUserDetails(claims);

                    try {
                        //transforms the UserDetails in a JSON object encoded in base64
                        exchange.getAttributes().put(BO_USER_DETAILS, Base64.getEncoder().encodeToString(mapper.writeValueAsBytes(userDetails)));
                    } catch (JsonProcessingException e) {
                        log.error("Unexpected error while trying to build the permission cookie.", e);
                        sink.error(new RuntimeException("Unexpected error while trying to build the permission cookie.", e));
                        return;
                    }
                    sink.next(exchange);
                })
                .flatMap(chain::filter);
    }

    @SuppressWarnings("unchecked")
    private UserDetails buildUserDetails(Map<String, Object> claims) {
        return new UserDetails(
                extractStringClaimOrEmpty("given_name", claims),
                extractStringClaimOrEmpty("family_name", claims),
                extractStringClaimOrEmpty("email", claims),
                (Map<String, ArrayList<String>>) Optional.ofNullable(claims.get("permissions")).orElse(Collections.emptyMap()));
    }

    private String extractStringClaimOrEmpty(String claimName, Map<String, Object> claims) {
        return Optional.ofNullable(claims.get(claimName)).map(Object::toString).orElse("");
    }

    public static class Config {
    }

    private record UserDetails(String firstName, String lastName, String email, Map<String, ArrayList<String>> permissions) {}
}
