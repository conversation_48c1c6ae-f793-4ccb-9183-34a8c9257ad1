package com.propero.bogateway.filters;

import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.oauth2.core.OAuth2AuthenticatedPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;

import java.util.Map;

public class FilterUtil {

    public static Map<String,Object> getTokenAttributes(AbstractAuthenticationToken token) {
        return switch (token.getPrincipal()) {
            case Jwt jwt -> jwt.getClaims();
            case OAuth2AuthenticatedPrincipal oauth -> oauth.getAttributes();
            default -> Map.of();
        };
    }
}
