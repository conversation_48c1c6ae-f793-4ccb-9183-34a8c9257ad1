package com.propero.bogateway.filters;

import io.opentelemetry.api.baggage.Baggage;
import io.opentelemetry.context.Scope;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@Component
@RequiredArgsConstructor
@Order(Ordered.HIGHEST_PRECEDENCE + 1000)
public class TracingBaggageFilter implements GlobalFilter {

    public static final String SESSION_ID_BAGGAGE = "session_id";
    private static final String KEYCLOAK_SESSION_ID_CLAIM = "sid";

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        return exchange.getPrincipal()
                .cast(AbstractAuthenticationToken.class)
                .map(FilterUtil::getTokenAttributes)
                .map(attributes -> Baggage.current().toBuilder()
                        .put(SESSION_ID_BAGGAGE, extractSessionId(attributes))
                        .build())
                .flatMap(baggage -> Mono.using(
                        baggage::makeCurrent,
                        scope -> chain.filter(exchange)
                                .doOnSuccess(v -> log.debug("Adding baggage [{}] to context", baggage))
                                .thenReturn(true),
                        Scope::close))
                .switchIfEmpty(chain.filter(exchange)
                        .doOnSuccess(v -> log.debug("No baggage headers, proceeding without baggage"))
                        .thenReturn(false))
                .then();
    }

    private String extractSessionId(Map<String, Object> tokenAttributes) {
        // Extract from JWT claims if 'sid' is included
        return Optional.ofNullable(tokenAttributes.get(KEYCLOAK_SESSION_ID_CLAIM))
                .map(String::valueOf)
                .orElseGet(() -> {
                    // Fallback: generate random session ID
                    log.debug("No session ID found in token, generating random one");
                    return UUID.randomUUID().toString();
                });
    }

}