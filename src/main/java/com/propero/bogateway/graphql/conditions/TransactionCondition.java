package com.propero.bogateway.graphql.conditions;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_DEFAULT)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TransactionCondition {

    private final EqualCondition<Boolean> demo = EqualCondition.of(false);
    private final EqualCondition<Boolean> compensating = EqualCondition.of(false);

    private EqualCondition<String> currency;
    private EqualCondition<String> providerTransactionType;
    private EqualCondition<String> operationType;
    private EqualCondition<String> license;
    private EqualCondition<String> internalTransactionId;
    private EqualCondition<String> status;
    private BetweenCondition<BigDecimal> delta;
    private BetweenCondition<String> transactionDate;
    private BetweenCondition<String> updatedAt;
}
