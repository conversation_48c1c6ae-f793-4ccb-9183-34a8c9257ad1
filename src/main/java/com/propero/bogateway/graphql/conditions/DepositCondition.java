package com.propero.bogateway.graphql.conditions;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.propero.bogateway.graphql.GraphQLCondition;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Objects;
import java.util.stream.Stream;

@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_DEFAULT)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DepositCondition implements GraphQLCondition<DepositCondition>  {

    private BetweenCondition<String> timestamp;

    @Override
    public boolean isEmpty() {
        return Stream.of(timestamp).filter(Objects::nonNull).allMatch(GraphQLCondition::isEmpty);
    }
}
