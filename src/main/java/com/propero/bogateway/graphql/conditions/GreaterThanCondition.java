package com.propero.bogateway.graphql.conditions;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.propero.bogateway.graphql.GraphQLCondition;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
@AllArgsConstructor(staticName = "of")
public class GreaterThanCondition<T> implements GraphQLCondition<GreaterThanCondition<T>> {
    @JsonProperty("_gte")
    private T value;

    @Override
    public boolean isEmpty() {
        return value == null;
    }
}
