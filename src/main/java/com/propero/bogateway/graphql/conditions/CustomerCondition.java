package com.propero.bogateway.graphql.conditions;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.propero.bogateway.graphql.GraphQLCondition;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Objects;
import java.util.stream.Stream;

@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_DEFAULT)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CustomerCondition implements GraphQLCondition<CustomerCondition> {
    private EqualCondition<Long> id;
    private LikeCondition email;
    private LikeCondition companyName;
    private EqualCondition<String> onboardingStatus;
    private EqualCondition<String> accessStatus;
    private EqualCondition<String> accessStatusReason;
    private EqualCondition<String> customerType;
    private EqualCondition<Boolean> flagged;
    private EqualCondition<String> registrationMethod;
    private BetweenCondition<String> createdAt;
    private PersonCondition person;
    private UserCondition user;

    @Override
    public boolean isEmpty() {
        return Stream.of(id, email, companyName, onboardingStatus, accessStatus, accessStatusReason,
                        customerType, flagged, registrationMethod, createdAt, person, user)
                .filter(Objects::nonNull)
                .allMatch(GraphQLCondition::isEmpty);
    }
}
