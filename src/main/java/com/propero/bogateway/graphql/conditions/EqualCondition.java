package com.propero.bogateway.graphql.conditions;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.propero.bogateway.graphql.GraphQLCondition;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
@AllArgsConstructor(staticName = "of")
public class EqualCondition<T> implements GraphQLCondition<EqualCondition<T>> {
    @JsonProperty("_eq")
    private final T value;

    @Override
    public boolean isEmpty() {
        return value == null;
    }
}
