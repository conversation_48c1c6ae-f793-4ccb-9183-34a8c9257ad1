package com.propero.bogateway.graphql.conditions;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.propero.bogateway.graphql.GraphQLCondition;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

import static java.util.Optional.ofNullable;

@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class BetweenCondition<T> implements GraphQLCondition<BetweenCondition<T>> {
    @JsonProperty("_gte")
    private T start;
    @JsonProperty("_lte")
    private T end;

    @Override
    public boolean isEmpty() {
        return start == null && end == null;
    }

    public static BetweenCondition<String> of(ZonedDateTime start, ZonedDateTime end) {
        return BetweenCondition.of(
            ofNullable(start).map(s -> s.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)).orElse(null),
            ofNullable(end).map(e -> e.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)).orElse(null)
        );
    }
}
