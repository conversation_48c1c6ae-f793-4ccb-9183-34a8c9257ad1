package com.propero.bogateway.graphql.conditions;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.propero.bogateway.graphql.GraphQLCondition;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import static java.util.Optional.ofNullable;

@Getter
@ToString
@AllArgsConstructor(staticName = "of")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LikeCondition implements GraphQLCondition<LikeCondition> {
    @JsonProperty("_like")
    private String value;
    @JsonProperty("_ilike")
    private String valueCaseInsensitive;

    @Override
    public boolean isEmpty() {
        return value == null && valueCaseInsensitive == null;
    }

    public static LikeCondition ofWildcard(String like) {
        return ofNullable(like)
                .map(c -> LikeCondition.of("%" + c + "%", null))
                .orElse(LikeCondition.of(like, null));
    }
    public static LikeCondition ofWildcardCaseInsensitive(String like) {
        return ofNullable(like)
                .map(c -> LikeCondition.of(null, "%" + c +"%"))
                .orElse(LikeCondition.of(null, like));
    }
}
