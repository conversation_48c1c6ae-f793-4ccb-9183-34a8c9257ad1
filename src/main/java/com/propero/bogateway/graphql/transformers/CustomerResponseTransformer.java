package com.propero.bogateway.graphql.transformers;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jayway.jsonpath.DocumentContext;
import com.propero.bogateway.graphql.GraphQLTransformer;
import com.propero.bogateway.helpers.GraphQLHelper;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.reactivestreams.Publisher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@RequiredArgsConstructor
public class CustomerResponseTransformer implements GraphQLTransformer<Map<String,Object>, CustomerResponseTransformer.CustomerResponse> {

    private final GraphQLHelper graphQLHelper;
    private final ObjectMapper objectMapper;

    @Override
    public Publisher<CustomerResponse> transform(ServerWebExchange exchange, Map<String, Object> data) {
        DocumentContext bodyDoc = graphQLHelper.parse(data);

        Integer totalCount;
        List<Map<String,Object>> customersRepresentation;
        if (bodyDoc.read("$.data.customer_db") != null && bodyDoc.read("$.data.customer_db.customers") != null) {
            // BackOfficeCustomerSearch.graphql
            totalCount = bodyDoc.read("$.data.customer_db.stats.aggregate.count");
            customersRepresentation = bodyDoc.read("$.data.customer_db.customers");

        } else if (bodyDoc.read("$.data.customer_db") != null && bodyDoc.read("$.data.customer_db.accounts") != null) {
            // BackOfficeCustomerTradingAccountSearch.graphql
            totalCount = bodyDoc.read("$.data.customer_db.stats.aggregate.count");
            customersRepresentation = bodyDoc.read("$.data.customer_db.accounts[*].customer");

        } else if (bodyDoc.read("$.data.warehouse_db") != null) {
            // BackOfficeCustomerDepositSearch.graphql
            totalCount = bodyDoc.read("$.data.warehouse_db.stats.aggregate.count");
            customersRepresentation = bodyDoc.read("$.data.warehouse_db.customers[*].customer");

        } else {
            log.error("Failed to process Hasura response. Body document format unknown.");
            return Mono.error(new Exception("Unknown response"));
        }

        List<Customer> customers = new ArrayList<>();
        for (Map<String, Object> customerRepresentation : customersRepresentation) {
            DocumentContext customerDoc = graphQLHelper.parse(customerRepresentation);
            Customer customer = objectMapper.convertValue(customerRepresentation, Customer.class);

            String name = Stream
                .of(customerDoc.read("fullName.firstName", String.class), customerDoc.read("fullName.lastName", String.class))
                .filter(Objects::nonNull)
                .collect(Collectors.joining(" "));
            customer.setName(name.isBlank() ? null : name);

            customer.setTradingAccountType(switch (customer.getOnboardingStatus()) {
                case "DEMO_REGISTRATION_STARTED" -> null;
                case "DEMO_REGISTRATION_COMPLETE", "REAL_REGISTRATION_STARTED" -> "DEMO";
                default -> "REAL";
            });

            customer.setTotalDeposit(customerDoc.read("$.deposits.aggregate.sum.amount", BigDecimal.class));

            customers.add(customer);
        }

        return Mono.just(CustomerResponse.builder()
                .customers(customers)
                .totalCount(totalCount)
                .build());
    }

    @Getter
    @Builder
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public static class CustomerResponse {
        private Integer totalCount;
        private List<Customer> customers;
    }

    @Getter
    @Builder
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    private static class Customer {
        private String id;
        private String legalEntity;
        private String email;
        private String brand;
        private String created;
        private String registrationMethod;
        private String onboardingStatus;
        private String currency;
        private Map<String,Object> kycSummary;

        @Setter
        private String name;
        @Setter
        private BigDecimal totalDeposit;
        @Setter
        private String tradingAccountType;
    }
}
