package com.propero.bogateway.graphql.transformers;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.propero.bogateway.graphql.GraphQLQuery;
import com.propero.bogateway.graphql.GraphQLTransformer;
import com.propero.bogateway.graphql.conditions.BetweenCondition;
import com.propero.bogateway.graphql.conditions.CustomerCondition;
import com.propero.bogateway.graphql.conditions.DepositCondition;
import com.propero.bogateway.graphql.conditions.EqualCondition;
import com.propero.bogateway.graphql.conditions.LikeCondition;
import com.propero.bogateway.graphql.conditions.PersonCondition;
import com.propero.bogateway.graphql.conditions.TradingAccountCondition;
import com.propero.bogateway.graphql.conditions.UserCondition;
import com.propero.bogateway.helpers.GraphQLHelper;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.ToString;
import org.reactivestreams.Publisher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.time.ZonedDateTime;
import java.util.Objects;

@RequiredArgsConstructor
@SuppressWarnings("rawtypes")
public class CustomerQueryTransformer implements GraphQLTransformer<CustomerQueryTransformer.BOCustomerSearchQuery, GraphQLQuery> {

    private final GraphQLHelper graphQLHelper;

    @Override
    public Publisher<GraphQLQuery> transform(ServerWebExchange exchange, BOCustomerSearchQuery query) {
        if (query == null) {
            return Mono.empty();
        }

        if (query.getFirstDepositBefore() != null || query.getFirstDepositAfter() != null) {
            return transformDepositSearchQuery(query);
        }

        if (query.getExternalAccountId() != null || query.getRealAccountCreatedBefore() != null || query.getRealAccountCreatedAfter() != null) {
            return transformTradingAccountSearchQuery( query);
        }

        return transformCustomerSearchQuery(query);
    }

    private Publisher<GraphQLQuery> transformCustomerSearchQuery( BOCustomerSearchQuery query) {
        PersonCondition person = PersonCondition.builder()
                .firstName(LikeCondition.ofWildcard(query.getFirstName()).value())
                .lastName(LikeCondition.ofWildcard(query.getLastName()).value())
                .country(EqualCondition.of(query.getCountryOfResidence()).value())
                .build();

        CustomerCondition customer = CustomerCondition.builder()
                .id(EqualCondition.of(query.getCustomerId()).value())
                .flagged(EqualCondition.of(query.getFlagged()).value())
                .companyName(LikeCondition.ofWildcard(query.getCompanyName()).value())
                .onboardingStatus(EqualCondition.of(query.getOnboardingStatus()).value())
                .accessStatus(EqualCondition.of(query.getAccessStatus()).value())
                .accessStatusReason(EqualCondition.of(query.getAccessStatusReason()).value())
                .registrationMethod(EqualCondition.of(query.getRegistrationMethod()).value())
                .customerType(EqualCondition.of(query.getCustomerType()).value())
                .person(person.value())
                .user(UserCondition.builder().emailPrincipal(LikeCondition.ofWildcardCaseInsensitive(query.getEmail()).value()).build().value())
                .createdAt(BetweenCondition.of(query.getCustomerCreatedAfter(), query.getCustomerCreatedBefore()).value())
                .build();

        HasuraQueryVariables queryVariables = HasuraQueryVariables.builder()
                .customerCondition(customer)
                .pageOffset(query.getOffset())
                .pageSize(query.getPerPage())
                .build();

        return graphQLHelper.query("BackOfficeCustomerSearch", queryVariables).cast(GraphQLQuery.class);
    }

    private Publisher<GraphQLQuery> transformDepositSearchQuery(BOCustomerSearchQuery query) {
        PersonCondition person = PersonCondition.builder()
                .firstName(LikeCondition.ofWildcard(query.getFirstName()).value())
                .lastName(LikeCondition.ofWildcard(query.getLastName()).value())
                .country(EqualCondition.of(query.getCountryOfResidence()).value())
                .build();

        CustomerCondition customer = CustomerCondition.builder()
                .id(EqualCondition.of(query.getCustomerId()).value())
                .email(LikeCondition.ofWildcardCaseInsensitive(query.getEmail()).value())
                .flagged(EqualCondition.of(query.getFlagged()).value())
                .companyName(LikeCondition.ofWildcard(query.getCompanyName()).value())
                .onboardingStatus(EqualCondition.of(query.getOnboardingStatus()).value())
                .accessStatus(EqualCondition.of(query.getAccessStatus()).value())
                .accessStatusReason(EqualCondition.of(query.getAccessStatusReason()).value())
                .registrationMethod(EqualCondition.of(query.getRegistrationMethod()).value())
                .customerType(EqualCondition.of(query.getCustomerType()).value())
                .person(person.value())
                .createdAt(BetweenCondition.of(query.getCustomerCreatedAfter(), query.getCustomerCreatedBefore()).value())
                .build();

        DepositCondition deposit = DepositCondition.builder()
                .timestamp(BetweenCondition.of(query.getFirstDepositAfter(), query.getFirstDepositBefore()).value())
                .build();

        HasuraQueryVariables queryVariables = HasuraQueryVariables.builder()
                .customerCondition(customer)
                .depositCondition(deposit)
                .pageOffset(query.getOffset())
                .pageSize(query.getPerPage())
                .build();

        return graphQLHelper.query( "BackOfficeCustomerDepositSearch", queryVariables).cast(GraphQLQuery.class);
    }

    private Publisher<GraphQLQuery> transformTradingAccountSearchQuery(BOCustomerSearchQuery query) {
        TradingAccountCondition tradingAccount = TradingAccountCondition.builder()
                .provider(EqualCondition.of(query.getAccountProvider()).value())
                .externalAccountId(EqualCondition.of(query.getExternalAccountId()).value())
                .real(EqualCondition.of(query.getReal()).value())
                .createdAt(BetweenCondition.of(query.getRealAccountCreatedAfter(), query.getRealAccountCreatedBefore()).value())
                .build();

        HasuraQueryVariables queryVariables = HasuraQueryVariables.builder()
                .tradingAccountCondition(tradingAccount)
                .pageOffset(query.getOffset())
                .pageSize(query.getPerPage())
                .build();

        return graphQLHelper.query("BackOfficeCustomerTradingAccountSearch", queryVariables).cast(GraphQLQuery.class);
    }

    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BOCustomerSearchQuery {

        private Long customerId;
        private String externalAccountId;
        private String firstName;
        private String lastName;

        private String email;
        private String companyName;
        private String onboardingStatus;
        private String accessStatus;
        private String accessStatusReason;
        private String customerType;

        private Boolean real;
        private Boolean flagged;

        private String accountProvider;

        private String countryOfResidence;

        private String registrationMethod;

        private ZonedDateTime customerCreatedAfter;
        private ZonedDateTime customerCreatedBefore;

        private ZonedDateTime realAccountCreatedAfter;
        private ZonedDateTime realAccountCreatedBefore;

        private ZonedDateTime firstDepositAfter;
        private ZonedDateTime firstDepositBefore;

        private Integer perPage;
        private Integer page;

        private Integer getPerPage() {
            return Objects.requireNonNullElse(perPage, 50);
        }

        private Integer getOffset() {
            if (page == null || page <= 1) {
                return 0;
            }
            return getPerPage() * (page - 1);
        }
    }

    @Getter
    @Builder
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private static class HasuraQueryVariables {
        private CustomerCondition customerCondition;
        private TradingAccountCondition tradingAccountCondition;
        private DepositCondition depositCondition;
        private Integer pageSize;
        private Integer pageOffset;
    }
}
