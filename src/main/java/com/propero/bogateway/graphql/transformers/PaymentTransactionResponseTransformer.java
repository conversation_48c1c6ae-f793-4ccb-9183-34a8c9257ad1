package com.propero.bogateway.graphql.transformers;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jayway.jsonpath.DocumentContext;
import com.propero.bogateway.graphql.GraphQLTransformer;
import com.propero.bogateway.helpers.GraphQLHelper;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.reactivestreams.Publisher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RequiredArgsConstructor
public class PaymentTransactionResponseTransformer implements
        GraphQLTransformer<Map<String,Object>, PaymentTransactionResponseTransformer.PaymentTransactionResponse> {

    private final GraphQLHelper graphQLHelper;
    private final ObjectMapper objectMapper;

    @Override
    public Publisher<PaymentTransactionResponse> transform(ServerWebExchange exchange, Map<String, Object> data) {
        DocumentContext bodyDoc = graphQLHelper.parse(data);

        Integer totalCount = bodyDoc.read("$.data.results.stats.aggregate.count");
        List<Map<String,Object>> results = bodyDoc.read("$.data.results.transactions");

        List<PaymentTransactionResponse.Transaction> transactions = new ArrayList<>();
        for (Map<String, Object> transactionRepresentation : results) {
            DocumentContext transactionDoc = graphQLHelper.parse(transactionRepresentation);
            PaymentTransactionResponse.Transaction transaction = objectMapper.convertValue(transactionRepresentation, PaymentTransactionResponse.Transaction.class);

            String name = Stream
                    .of(transactionDoc.read("customer.fullName.firstName", String.class), transactionDoc.read("customer.fullName.lastName", String.class))
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(" "));
            transaction.setName(name.isBlank() ? null : name);

            transactions.add(transaction);
        }

        return Mono.just(PaymentTransactionResponse.builder().transactions(transactions).totalCount(totalCount).build());
    }

    @Getter
    @Builder
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public static class PaymentTransactionResponse {
        private List<Transaction> transactions;
        private Integer totalCount;

        @Getter
        @Builder
        @AllArgsConstructor(access = AccessLevel.PRIVATE)
        private static class BankDetails {
            private String iban;
            private String bankName;
            private String swiftCode;
            private String bankAccountName;
            private String bankAccountNumber;
            private String bankAccountAddress;
        }

        @Getter
        @Builder
        @AllArgsConstructor(access = AccessLevel.PRIVATE)
        private static class Transaction {
            private String transactionId;
            private Long customerId;
            private String reference;
            private String currency;
            private BigDecimal amount;
            private String paymentMethod;
            private String psp;
            private String status;
            private String createdAt;
            private String updatedAt;
            private BankDetails bankInfo;

            @Setter
            private String name;
        }
    }
}
