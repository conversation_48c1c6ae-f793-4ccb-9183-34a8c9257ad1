package com.propero.bogateway.graphql.transformers;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.propero.bogateway.graphql.GraphQLQuery;
import com.propero.bogateway.graphql.GraphQLTransformer;
import com.propero.bogateway.graphql.conditions.BetweenCondition;
import com.propero.bogateway.graphql.conditions.CustomerCondition;
import com.propero.bogateway.graphql.conditions.EqualCondition;
import com.propero.bogateway.graphql.conditions.LikeCondition;
import com.propero.bogateway.graphql.conditions.PersonCondition;
import com.propero.bogateway.graphql.conditions.TransactionCondition;
import com.propero.bogateway.helpers.GraphQLHelper;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.ToString;
import org.reactivestreams.Publisher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

@RequiredArgsConstructor
@SuppressWarnings("rawtypes")
public class TransactionQueryTransformer implements GraphQLTransformer<TransactionQueryTransformer.BOTransactionSearchQuery, GraphQLQuery> {

    private final GraphQLHelper graphQLHelper;

    @Override
    public Publisher<GraphQLQuery> transform(ServerWebExchange exchange, BOTransactionSearchQuery query) {
        if (query == null) {
            return Mono.empty();
        }

        PersonCondition person = PersonCondition.builder()
                .firstName(LikeCondition.ofWildcard(query.getFirstName()).value())
                .lastName(LikeCondition.ofWildcard(query.getLastName()).value())
                .build();
        CustomerCondition customer = CustomerCondition.builder()
                .id(EqualCondition.of(query.getCustomerId()).value())
                .email(LikeCondition.ofWildcard(query.getEmail()).value())
                .person(person.value())
                .build();
        TransactionCondition transactions = TransactionCondition.builder()
                .currency(EqualCondition.of(query.getCurrency()).value())
                .internalTransactionId(EqualCondition.of(query.getTransactionId()).value())
                .providerTransactionType(EqualCondition.of(query.getTransactionType()).value())
                .status(EqualCondition.of(query.getStatus()).value())
                .delta(BetweenCondition.of(query.getAmountFrom(), query.getAmountTo()).value())
                .transactionDate(BetweenCondition.of(query.getFromTimestamp(), query.getToTimestamp()).value())
                .updatedAt(BetweenCondition.of(query.getUpdatedFrom(), query.getUpdatedTo()).value())
                .build();
        HasuraTransactionVariables variables = HasuraTransactionVariables.builder()
                .customerCondition(customer.value())
                .transactionCondition(transactions)
                .offset(calculateOffset(query.getPage(), query.getSize()))
                .size(query.getSize() == null ? 100 : query.getSize())
                .sortBy(query.getSortOrder() == null ? "desc" : query.getSortOrder().toLowerCase())
                .build();

        return graphQLHelper
                .query(customer.isEmpty() ? "BackOfficeTransactionsByTransaction" : "BackOfficeTransactionsByCustomer", variables)
                .cast(GraphQLQuery.class);
    }

    private int calculateOffset(Integer page, Integer size) {
        if (page == null || size == null) {
            return 0;
        }
        return (page - 1) * size;
    }

    @Getter
    @AllArgsConstructor
    public static class BOTransactionSearchQuery {
        private Long customerId;
        private String currency;
        private String email;
        private String firstName;
        private String lastName;
        private ZonedDateTime fromTimestamp;
        private ZonedDateTime toTimestamp;
        private ZonedDateTime updatedTo;
        private ZonedDateTime updatedFrom;
        private String status;
        private String transactionId;
        private String transactionType;
        private String operationType;
        private BigDecimal amountFrom;
        private BigDecimal amountTo;
        private Integer size;
        private Integer page;
        private String sortOrder;
    }

    @Getter
    @Builder
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private static class HasuraTransactionVariables {
        private CustomerCondition customerCondition;
        private TransactionCondition transactionCondition;
        private Integer size;
        private Integer offset;
        private String sortBy;
    }
}
