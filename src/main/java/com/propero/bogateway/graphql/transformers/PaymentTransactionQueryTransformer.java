package com.propero.bogateway.graphql.transformers;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.propero.bogateway.graphql.GraphQLQuery;
import com.propero.bogateway.graphql.GraphQLTransformer;
import com.propero.bogateway.graphql.conditions.BetweenCondition;
import com.propero.bogateway.graphql.conditions.EqualCondition;
import com.propero.bogateway.graphql.conditions.NullCondition;
import com.propero.bogateway.graphql.conditions.PaymentTransactionCondition;
import com.propero.bogateway.helpers.GraphQLHelper;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.ToString;
import lombok.With;
import org.reactivestreams.Publisher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.time.ZonedDateTime;

@RequiredArgsConstructor
@SuppressWarnings("rawtypes")
public class PaymentTransactionQueryTransformer implements GraphQLTransformer<PaymentTransactionQueryTransformer.BOPaymentTransactionSearchQuery, GraphQLQuery> {

    private final GraphQLHelper graphQLHelper;

    @Override
    public Publisher<GraphQLQuery> transform(ServerWebExchange exchange, BOPaymentTransactionSearchQuery query) {
        if (query == null) {
            return Mono.empty();
        }

        PaymentTransactionCondition transactions = PaymentTransactionCondition.builder()
                .status(EqualCondition.of(query.getStatus().toUpperCase()).value())
                .transactionType(EqualCondition.of(query.getType().toUpperCase()).value())
                .createdAt(BetweenCondition.of(query.getFromTimestamp(), query.getToTimestamp()).value())
                .manualBankInfo(query.getManual() != null ? NullCondition.of(!query.getManual()) : null)
                .build();

        HasuraTransactionVariables variables = HasuraTransactionVariables.builder()
                .transactionCondition(transactions)
                .pageOffset(calculateOffset(query.getPage(), query.getSize()))
                .pageSize(query.getSize() == null ? 20 : query.getSize())
                .sortBy(query.getSortOrder() == null ? "desc" : query.getSortOrder().toLowerCase())
                .build();

        return graphQLHelper.query("BackOfficePaymentTransactionSearch", variables).cast(GraphQLQuery.class);
    }

    private int calculateOffset(Integer page, Integer size) {
        if (page == null || size == null) {
            return 0;
        }
        return (page - 1) * size;
    }

    @With
    @Getter
    @AllArgsConstructor
    public static class BOPaymentTransactionSearchQuery {
        private ZonedDateTime fromTimestamp;
        private ZonedDateTime toTimestamp;
        private Integer size;
        private Integer page;
        private String sortOrder;
        private Boolean manual;
        @JsonIgnore private String status;
        @JsonIgnore private String type;
    }

    @Getter
    @Builder
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    public static class HasuraTransactionVariables {
        private PaymentTransactionCondition transactionCondition;
        private Integer pageSize;
        private Integer pageOffset;
        private String sortBy;
    }
}
