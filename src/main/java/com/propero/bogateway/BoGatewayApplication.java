package com.propero.bogateway;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
@OpenAPIDefinition(
    info = @Info(title = "BO Gateway API", version = "1.0", description = "Documentation BO Gateway API v1.0")
)
public class BoGatewayApplication {

    public static void main(String[] args) {
        SpringApplication.run(BoGatewayApplication.class, args);
    }
}
