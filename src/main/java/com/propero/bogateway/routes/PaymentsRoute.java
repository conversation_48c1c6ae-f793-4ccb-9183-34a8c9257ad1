package com.propero.bogateway.routes;

import com.propero.bogateway.config.AccessPermissions;
import com.propero.bogateway.config.ApiProps;
import com.propero.bogateway.helpers.RouteHelper;
import com.propero.bogateway.helpers.SecurityHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;

@Configuration
@RequiredArgsConstructor
public class PaymentsRoute {

    private final SecurityHelper securityHelper;
    private final RouteHelper routeHelper;
    private final ApiProps apiProps;

    @Bean
    public RouteLocator paymentsRoutes(RouteLocatorBuilder builder) {
        return builder.routes()
                .route("update-manual-tx", r -> r.path(routeHelper.prefixRoutes("/payment",
                                "/bo/manual/customer/{customerId}/transaction/{txId}",
                                "/manual/deposit/{id}"))
                        .and()
                        .method(HttpMethod.PATCH)
                        .filters(fSpec -> fSpec.filter(securityHelper.authIfHasAnyRole(AccessPermissions.WRITE_TX)))
                        .uri(apiProps.getUris().getPaymentServiceUri()))

                .route("common-get", r -> r.path(routeHelper.prefixRoutes("/payment",
                                "/bo/transactions/firstDepositors",
                                "/customer/{customerId}/account/{accountId}/payment-methods"))
                        .and()
                        .method(HttpMethod.GET)
                        .filters(fSpec -> fSpec.filter(securityHelper.authIfHasAnyRole(AccessPermissions.READ_TX)))
                        .uri(apiProps.getUris().getPaymentServiceUri()))

                .route("get-transactions", r -> r.path(routeHelper.prefixRoute("/payment/transactions/**"))
                        .and()
                        .method(HttpMethod.GET)
                        .filters(fSpec -> routeHelper.stripPrefix(fSpec, 1)
                                .prefixPath("/payment/bo")
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.READ_TX)))
                        .uri(apiProps.getUris().getPaymentServiceUri()))

                .route("create-tx", r -> r.path(routeHelper.prefixRoutes("/payment",
                                "/customer/{customerId}/account/{accountId}/withdrawal",
                                "/manual/customer/{customerId}/account/{accountId}/transaction",
                                "/manual/deposit"))
                        .and()
                        .method(HttpMethod.POST)
                        .filters(fSpec -> fSpec.filter(securityHelper.authIfHasAnyRole(AccessPermissions.WRITE_TX)))
                        .uri(apiProps.getUris().getPaymentServiceUri()))

                .route("update-tx", r -> r.path(routeHelper.prefixRoute("/payment/transactions/{id}/destination-deposit"))
                        .and()
                        .method(HttpMethod.POST)
                        .filters(fSpec -> routeHelper.stripPrefix(fSpec)
                                .prefixPath("/payment/bo")
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.WRITE_TX)))
                        .uri(apiProps.getUris().getPaymentServiceUri()))
                .build();
    }
}
