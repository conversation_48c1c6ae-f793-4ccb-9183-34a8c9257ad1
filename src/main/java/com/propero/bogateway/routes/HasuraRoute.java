package com.propero.bogateway.routes;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.propero.bogateway.config.AccessPermissions;
import com.propero.bogateway.config.ApiProps;
import com.propero.bogateway.graphql.GraphQLQuery;
import com.propero.bogateway.graphql.transformers.CustomerQueryTransformer;
import com.propero.bogateway.graphql.transformers.CustomerQueryTransformer.BOCustomerSearchQuery;
import com.propero.bogateway.graphql.transformers.CustomerResponseTransformer;
import com.propero.bogateway.graphql.transformers.CustomerResponseTransformer.CustomerResponse;
import com.propero.bogateway.graphql.transformers.PaymentTransactionQueryTransformer;
import com.propero.bogateway.graphql.transformers.PaymentTransactionQueryTransformer.BOPaymentTransactionSearchQuery;
import com.propero.bogateway.graphql.transformers.PaymentTransactionResponseTransformer;
import com.propero.bogateway.graphql.transformers.PaymentTransactionResponseTransformer.PaymentTransactionResponse;
import com.propero.bogateway.graphql.transformers.TransactionQueryTransformer;
import com.propero.bogateway.graphql.transformers.TransactionQueryTransformer.BOTransactionSearchQuery;
import com.propero.bogateway.graphql.transformers.TransactionsResponseTransformer;
import com.propero.bogateway.graphql.transformers.TransactionsResponseTransformer.TransactionResponse;
import com.propero.bogateway.helpers.GraphQLHelper;
import com.propero.bogateway.helpers.RouteHelper;
import com.propero.bogateway.helpers.SecurityHelper;
import io.swagger.v3.oas.models.Operation;
import io.swagger.v3.oas.models.parameters.RequestBody;
import io.swagger.v3.oas.models.responses.ApiResponse;
import io.swagger.v3.oas.models.responses.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.propero.bogateway.docs.OpenApiConfiguration.API_DOCS;
import static com.propero.bogateway.docs.OpenApiConfiguration.API_SCHEMAS;
import static com.propero.bogateway.docs.OpenApiConfiguration.getDocumentContent;
import static org.springframework.cloud.gateway.support.RouteMetadataUtils.CONNECT_TIMEOUT_ATTR;
import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.URI_TEMPLATE_VARIABLES_ATTRIBUTE;

@Slf4j
@Configuration
public class HasuraRoute {

    private static final String HASURA_GRAPHQL_ENDPOINT= "/v1/graphql";

    private final SecurityHelper securityHelper;
    private final RouteHelper routeHelper;
    private final ApiProps apiProps;

    private final TransactionsResponseTransformer transactionsResponseTransformer;
    private final TransactionQueryTransformer transactionQueryTransformer;

    private final PaymentTransactionResponseTransformer paymentTransactionResponseTransformer;
    private final PaymentTransactionQueryTransformer paymentTransactionQueryTransformer;

    private final CustomerQueryTransformer customerQueryTransformer;
    private final CustomerResponseTransformer customerResponseTransformer;

    public HasuraRoute(SecurityHelper securityHelper, RouteHelper routeHelper, ApiProps apiProps, GraphQLHelper graphQLHelper, ObjectMapper objectMapper) {
        this.securityHelper = securityHelper;
        this.routeHelper = routeHelper;
        this.apiProps = apiProps;
        this.transactionsResponseTransformer = new TransactionsResponseTransformer(graphQLHelper);
        this.transactionQueryTransformer = new TransactionQueryTransformer(graphQLHelper);
        this.customerQueryTransformer = new CustomerQueryTransformer(graphQLHelper);
        this.customerResponseTransformer = new CustomerResponseTransformer(graphQLHelper, objectMapper);
        this.paymentTransactionQueryTransformer = new PaymentTransactionQueryTransformer(graphQLHelper);
        this.paymentTransactionResponseTransformer = new PaymentTransactionResponseTransformer(graphQLHelper, objectMapper);
    }

    @Bean
    public RouteLocator transactions(RouteLocatorBuilder builder) {
        return builder.routes()
                .route("execute-hasura-transactions-query", r -> r.path(routeHelper.prefixRoute("/transactions"))
                        .and()
                        .method(HttpMethod.POST)
                        .filters(fSpec -> fSpec
                                .setPath(HASURA_GRAPHQL_ENDPOINT)
                                .modifyRequestBody(BOTransactionSearchQuery.class, GraphQLQuery.class, transactionQueryTransformer::transform)
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.READ_TX))
                                .modifyResponseBody(Map.class, TransactionResponse.class, transactionsResponseTransformer::transform))
                        .metadata(CONNECT_TIMEOUT_ATTR, apiProps.getTimeouts().getExtendedSearchTimeout())
                        .metadata(API_SCHEMAS, List.of(BOTransactionSearchQuery.class, TransactionResponse.class))
                        .metadata(API_DOCS, new Operation()
                                .description("Fetch transactions via Hasura")
                                .tags(List.of("hasura"))
                                .requestBody(new RequestBody().content(getDocumentContent("application/json", BOTransactionSearchQuery.class)))
                                .responses(new ApiResponses().addApiResponse("200", new ApiResponse().content(getDocumentContent("200", TransactionResponse.class)))))
                        .uri(apiProps.getUris().getHasuraServiceUri())
                )
                .build();
    }

    @Bean
    public RouteLocator transactionsByTypeAndStatus(RouteLocatorBuilder builder) {
        return builder.routes()
                .route("execute-hasura-payment-transactions", r -> r.path(routeHelper.prefixRoute("/transactions/{type}/{status}"))
                        .and()
                        .method(HttpMethod.POST)
                        .filters(fSpec -> fSpec
                                .setPath(HASURA_GRAPHQL_ENDPOINT)
                                .modifyRequestBody(BOPaymentTransactionSearchQuery.class, GraphQLQuery.class, (e, query) -> {
                                    Map<String, String> pathVariables = Objects.requireNonNull(e.getAttribute(URI_TEMPLATE_VARIABLES_ATTRIBUTE));
                                    String status = pathVariables.get("status").toUpperCase();
                                    String type = pathVariables.get("type").toUpperCase();
                                    return paymentTransactionQueryTransformer.transform(e, query.withStatus(status).withType(type));
                                })
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.READ_TX))
                                .modifyResponseBody(Map.class, PaymentTransactionResponse.class, paymentTransactionResponseTransformer::transform))
                        .metadata(CONNECT_TIMEOUT_ATTR, apiProps.getTimeouts().getExtendedSearchTimeout())
                        .metadata(API_SCHEMAS, List.of(BOPaymentTransactionSearchQuery.class, PaymentTransactionResponse.class))
                        .metadata(API_DOCS, new Operation()
                                .description("Fetch Payment transactions via Hasura by type and status")
                                .tags(List.of("hasura"))
                                .requestBody(new RequestBody().content(getDocumentContent("application/json", BOPaymentTransactionSearchQuery.class)))
                                .responses(new ApiResponses().addApiResponse("200", new ApiResponse().content(getDocumentContent("200", PaymentTransactionResponse.class)))))
                        .uri(apiProps.getUris().getHasuraServiceUri())
                )
                .build();
    }

    @Bean
    public RouteLocator customers(RouteLocatorBuilder builder) {
        return builder.routes()
                .route("execute-hasura-customer-query", r -> r.path(routeHelper.prefixRoute("/v2/customers"))
                        .and()
                        .method(HttpMethod.POST)
                        .filters(fSpec -> fSpec
                                .setPath(HASURA_GRAPHQL_ENDPOINT)
                                .modifyRequestBody(BOCustomerSearchQuery.class, GraphQLQuery.class, customerQueryTransformer::transform)
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.READ_TX))
                                .modifyResponseBody(Map.class, CustomerResponse.class, customerResponseTransformer::transform))
                        .metadata(CONNECT_TIMEOUT_ATTR, apiProps.getTimeouts().getExtendedSearchTimeout())
                        .metadata(API_SCHEMAS, List.of(BOCustomerSearchQuery.class, CustomerResponse.class))
                        .metadata(API_DOCS, new Operation()
                                .description("Fetch customers via Hasura")
                                .tags(List.of("hasura"))
                                .requestBody(new RequestBody().content(getDocumentContent("application/json", BOCustomerSearchQuery.class)))
                                .responses(new ApiResponses().addApiResponse("200", new ApiResponse().content(getDocumentContent("200", CustomerResponse.class)))))
                        .uri(apiProps.getUris().getHasuraServiceUri())
                )
                .build();
    }
}
