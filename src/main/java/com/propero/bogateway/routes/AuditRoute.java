package com.propero.bogateway.routes;

import com.propero.bogateway.config.AccessPermissions;
import com.propero.bogateway.config.ApiProps;
import com.propero.bogateway.helpers.RouteHelper;
import com.propero.bogateway.helpers.SecurityHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;

@Configuration
@RequiredArgsConstructor
public class AuditRoute {

    private final SecurityHelper securityHelper;
    private final RouteHelper routeHelper;
    private final ApiProps apiProps;

    @Bean
    public RouteLocator auditRoutes(RouteLocatorBuilder builder) {
        return builder.routes()
                .route("audit-routes", r -> r.path(routeHelper.prefixRoutes("/audit", "/agent-actions", "/messages"))
                        .and()
                        .method(HttpMethod.GET)
                        .filters(fSpec -> fSpec.filter(securityHelper.authIfHasAnyRole(AccessPermissions.READ_CUST)))
                        .uri(apiProps.getUris().getAuditServiceUri()))
                .build();
    }
}
