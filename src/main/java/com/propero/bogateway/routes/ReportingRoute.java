package com.propero.bogateway.routes;

import com.propero.bogateway.config.AccessPermissions;
import com.propero.bogateway.config.ApiProps;
import com.propero.bogateway.helpers.RouteHelper;
import com.propero.bogateway.helpers.SecurityHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;

import static org.springframework.cloud.gateway.support.RouteMetadataUtils.CONNECT_TIMEOUT_ATTR;

@Configuration
@RequiredArgsConstructor
public class ReportingRoute {

    private final SecurityHelper securityHelper;
    private final RouteHelper routeHelper;
    private final ApiProps apiProps;

    /**
     * @deprecated Replaced by Hasura GraphQL query in {@link HasuraRoute}
     */
    @Bean
    @Deprecated
    public RouteLocator reportingRoutes(RouteLocatorBuilder builder) {
        return builder.routes()
                .route("execute-reporting-query", r -> r.path(routeHelper.prefixRoute("/reports/queries/{queryType}"))
                        .and()
                        .method(HttpMethod.POST)
                        .filters(fSpec -> routeHelper.stripPrefix(fSpec, 1)
                                .prefixPath("/presto")
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.READ_TX)))
                        .metadata(CONNECT_TIMEOUT_ATTR, apiProps.getTimeouts().getExtendedSearchTimeout())
                        .uri(apiProps.getUris().getReportingServiceUri())
                )
                .build();
    }
}
