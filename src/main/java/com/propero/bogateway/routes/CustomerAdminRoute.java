package com.propero.bogateway.routes;

import com.propero.bogateway.config.AccessPermissions;
import com.propero.bogateway.config.ApiProps;
import com.propero.bogateway.helpers.RouteHelper;
import com.propero.bogateway.helpers.SecurityHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;

import static org.springframework.cloud.gateway.support.RouteMetadataUtils.CONNECT_TIMEOUT_ATTR;

@Configuration
@RequiredArgsConstructor
public class CustomerAdminRoute {

    private final SecurityHelper securityHelper;
    private final RouteHelper routeHelper;
    private final ApiProps apiProps;

    @Bean
    public RouteLocator customerAdminRoutes(RouteLocatorBuilder builder) {
        return builder.routes()
                // PUT /v2/admin/customers/{customer-id}/access-status
                // PUT /v2/admin/customers/{customer-id}/promotions-enabled
                // PUT /v2/admin/customers/{customer-id}/id-numbers
                .route("update-customer", r -> r.path(routeHelper.prefixRoute(
                                "/v2/admin/customers/{customerId}/access-status",
                                "/v2/admin/customers/{customerId}/promotions-enabled",
                                "/v2/admin/customers/{customerId}/id-numbers"))
                        .and()
                        .method(HttpMethod.PUT)
                        .filters(fSpec -> routeHelper.stripPrefix(fSpec)
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.WRITE_CUST)))
                        .uri(apiProps.getUris().getCustomerServiceUri()))

                // PUT /v2/admin/customers/{customer-id}/verification
                .route("update-customer-verification", r -> r.path(routeHelper.prefixRoute("/v2/admin/customers/{customerId}/verification"))
                        .and()
                        .method(HttpMethod.PUT)
                        .filters(fSpec -> routeHelper.stripPrefix(fSpec)
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.VERIFY_ACCOUNT)))
                        .uri(apiProps.getUris().getCustomerServiceUri()))

                // GET /v3/admin/customers/{customerId}/documents
                // GET /v3/admin/customers/{customerId}/documents/{id}
                .route("get-kyc-documents", r -> r.path(routeHelper.prefixRoute("/v3/admin/customers/{customer-id}/documents/**"))
                        .and()
                        .method(HttpMethod.GET)
                        .filters(fSpec -> routeHelper.stripPrefix(fSpec)
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.READ_KYC)))
                        .uri(apiProps.getUris().getCustomerServiceUri()))

                // PATCH /v3/admin/customers/{customer-id}/documents?doc-type={type}&status={status}
                // PATCH /v3/admin/customers/{customer-id}/documents/{doc-id}?status={status}
                .route("set-kyc-document-status", r -> r.path(routeHelper.prefixRoute("/v3/admin/customers/{customer-id}/documents/**"))
                        .and()
                        .method(HttpMethod.PATCH)
                        .filters(fSpec -> routeHelper.stripPrefix(fSpec)
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.WRITE_KYC)))
                        .uri(apiProps.getUris().getCustomerServiceUri()))

                // POST /v3/admin/customers/{customer-id}/documents/
                .route("upload-kyc-document", r -> r.path(routeHelper.prefixRoute("/v3/admin/customers/{customer-id}/documents"))
                        .and()
                        .method(HttpMethod.POST)
                        .filters(fSpec -> routeHelper.stripPrefix(fSpec)
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.WRITE_KYC)))
                        .uri(apiProps.getUris().getCustomerServiceUri()))

                // DELETE /v3/admin/customers/{customerId}/documents/{documentId}",
                .route("delete-kyc-document", r -> r.path(routeHelper.prefixRoute("/v3/admin/customers/{customerId}/documents/{documentId}"))
                        .and()
                        .method(HttpMethod.DELETE)
                        .filters(fSpec -> routeHelper.stripPrefix(fSpec)
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.DELETE_KYC)))
                        .uri(apiProps.getUris().getCustomerServiceUri()))

                // GET /v2/customers/{customer-id}
                // GET /v2/customers/{customer-id}/appropriateness
                // GET /v2/customers/{customer-id}/suitability-answers (deprecated)
                .route("get-customer", r -> r.path(routeHelper.prefixRoute("/v2/customers/{customer-id}/**"))
                        .and()
                        .method(HttpMethod.GET)
                        .filters(fSpec -> routeHelper.stripPrefix(fSpec)
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.READ_CUST)))
                        .uri(apiProps.getUris().getCustomerServiceUri()))

                // GET /v2/admin/customers/{customer-id}/directory-searches
                // GET /v2/admin/customers/{customer-id}/pep-and-sanctions-searches
                .route("get-customer-directory-pep-and-sanctions", r -> r.path(routeHelper.prefixRoute("/v2/admin/customers/{customer-id}/**"))
                        .and()
                        .method(HttpMethod.GET)
                        .filters(fSpec -> routeHelper.stripPrefix(fSpec)
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.READ_KYC)))
                        .metadata(CONNECT_TIMEOUT_ATTR, apiProps.getTimeouts().getExtendedSearchTimeout())
                        .uri(apiProps.getUris().getCustomerServiceUri())
                )

                // GET /v2/admin/customers/{customer-id}/id-numbers
                .route("get-customer-id-numbers", r -> r.path(routeHelper.prefixRoute("/v2/admin/customers/{customer-id}/id-numbers"))
                        .and()
                        .method(HttpMethod.GET)
                        .filters(fSpec -> routeHelper.stripPrefix(fSpec)
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.READ_CUST)))
                        .uri(apiProps.getUris().getCustomerServiceUri()))

                // POST /v2/admin/customers/{customer-id}/directory-searches/manual-se
                // POST /v2/admin/customers/{customer-id}/directory-searches/manual-no
                // POST /v2/admin/customers/{customer-id}/pep-and-sanctions-searches/manual
                .route("manual-searches-customer", r -> r.path(routeHelper.prefixRoute("/v2/admin/customers/{customer-id}/*-searches/manual*"))
                        .and()
                        .method(HttpMethod.POST)
                        .filters(fSpec -> routeHelper.stripPrefix(fSpec)
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.WRITE_KYC))
                        )
                        .uri(apiProps.getUris().getCustomerServiceUri()))

                // POST /v2/admin/preliminary-customers/{customer-id}/activation-code
                .route("create-new-activation-token", r -> r.path(routeHelper.prefixRoute("/v2/admin/preliminary-customers/{customer-id}/activation-code"))
                        .and()
                        .method(HttpMethod.PUT)
                        .filters(fSpec -> routeHelper.stripPrefix(fSpec)
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.WRITE_CUST)))
                        .uri(apiProps.getUris().getCustomerServiceUri()))

                .build();
    }
}
