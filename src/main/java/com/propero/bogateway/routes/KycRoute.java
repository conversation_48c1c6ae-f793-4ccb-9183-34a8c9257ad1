package com.propero.bogateway.routes;

import com.propero.bogateway.config.AccessPermissions;
import com.propero.bogateway.config.ApiProps;
import com.propero.bogateway.helpers.RouteHelper;
import com.propero.bogateway.helpers.SecurityHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;

@Configuration
@RequiredArgsConstructor
public class KycRoute {

    private final SecurityHelper securityHelper;
    private final RouteHelper routeHelper;
    private final ApiProps apiProps;

    @Bean
    public RouteLocator kycRoutes(RouteLocatorBuilder builder) {
        return builder.routes()
                // /customers/{customerId}
                // /customers/{customerId}/results/id-verification
                // /customers/{customerId}/results/watchlist
                .route("get-customer", r -> r.path(routeHelper.prefixRoute("/kyc/v1/admin/customers/{customerId}/**"))
                        .and()
                        .method(HttpMethod.GET)
                        .filters(fSpec -> routeHelper.stripPrefix(fSpec, 1)
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.READ_KYC)))
                        .uri(apiProps.getUris().getKycServiceUri()))

                .route("edit-customer-watchlist-status", r -> r.path(routeHelper.prefixRoute("/kyc/v1/admin/customers/{customerId}/status/watchlist"))
                        .and()
                        .method(HttpMethod.PUT)
                        .filters(fSpec -> routeHelper.stripPrefix(fSpec, 2)
                                .rewritePath("/admin/customers/(?<custId>\\d+)/status/watchlist", "/v2/admin/customers/$\\{custId}/watchlist-status")
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.WRITE_KYC)))
                        .uri(apiProps.getUris().getCustomerServiceUri()))
                .build();
    }
}
