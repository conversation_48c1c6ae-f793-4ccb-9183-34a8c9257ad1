package com.propero.bogateway.routes;

import com.propero.bogateway.config.AccessPermissions;
import com.propero.bogateway.config.ApiProps;
import com.propero.bogateway.helpers.RouteHelper;
import com.propero.bogateway.helpers.SecurityHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;

@Configuration
@RequiredArgsConstructor
public class CustomersRoute {

    private final SecurityHelper securityHelper;
    private final RouteHelper routeHelper;
    private final ApiProps apiProps;

    @Bean
    public RouteLocator customersRoutes(RouteLocatorBuilder builder) {
        return builder.routes()
                .route("edit-customer", r -> r.path(routeHelper.prefixRoute("/customers", "/{customerId}"))
                        .and()
                        .method(HttpMethod.PATCH)
                        .filters(fSpec -> fSpec.filter(securityHelper.authIfHasAnyRole(AccessPermissions.WRITE_CUST)))
                        .uri(apiProps.getUris().getCustomerServiceUri()))
                .route("edit-customer-verification-methods", r -> r.path(routeHelper.prefixRoute("/customers", "/{customerId}/*-verification-method")) // (identity|address)-verification-method
                        .and()
                        .method(HttpMethod.PATCH)
                        .filters(fSpec -> fSpec.filter(securityHelper.authIfHasAnyRole(AccessPermissions.WRITE_CUST)))
                        .uri(apiProps.getUris().getCustomerServiceUri()))
                .route("customers-forgot-passwd", r -> r.path(routeHelper.prefixRoutes("/customers", "/forgot-password"))
                        .and()
                        .method(HttpMethod.POST)
                        .filters(fSpec -> fSpec.rewritePath("/.*", "/authentication/customers/password/forgot-password")
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.CUSTOMER_RESET_PASSWORD)))
                        .uri(apiProps.getUris().getCustomerServiceUri()))
                .route("customers-search-edit", r -> r.path(routeHelper.prefixRoutes("/customers", "/search"))
                        .and()
                        .method(HttpMethod.PUT)
                        .filters(fSpec -> fSpec.filter(securityHelper.authIfHasAnyRole(AccessPermissions.READ_CUST)))
                        .uri(apiProps.getUris().getCustomerServiceUri()))
                .route("customers-get-balance-for-account", r -> r.path(routeHelper.prefixRoutes("/customers", "/{customer-id}/trading-accounts/{account-id}/balance"))
                        .and()
                        .method(HttpMethod.GET)
                        .filters(fSpec -> routeHelper.stripPrefix(fSpec, 1)
                                .rewritePath("/(?<custId>.*)/trading-accounts/(?<accountId>.*)/balance", "/v1/customers/$\\{custId}/accounts/$\\{accountId}/balance")
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.READ_CUST)))
                        .uri(apiProps.getUris().getTradingBalanceServiceUri())
                )
                .route("customers-get-suitability-regulatory-answers", r -> r.path(routeHelper.prefixRoutes("/customers", "/{customerId}/regulatory-answers/suitability-regulatory-answers"))
                        .and()
                        .method(HttpMethod.GET)
                        .filters(fSpec -> fSpec
                                .rewritePath("/(?<head>.*)/regulatory-answers/(?<tail>.*)", "/$\\{head}//$\\{tail}")
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.READ_CUST)))
                        .uri(apiProps.getUris().getCustomerServiceUri()))
                .route("customers-manual-search", r -> r.path(routeHelper.prefixRoutes("/customers", "/{customerId}/manual-*-search*"))
                        .and()
                        .method(HttpMethod.POST)
                        .filters(fSpec -> fSpec
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.READ_CUST)))
                        .uri(apiProps.getUris().getCustomerServiceUri()))
                .route("customers-comments", r -> r.path(routeHelper.prefixRoutes("/customers", "/{customerId}/comments"))
                        .and()
                        .method(HttpMethod.POST)
                        .filters(fSpec -> fSpec
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.WRITE_SUPPORT_COMMENT, AccessPermissions.WRITE_COMPLIANCE_COMMENT)))
                        .uri(apiProps.getUris().getCustomerServiceUri()))
                // common pass-through wildcard paths follow:
                .route("customers-get-routes", r -> r.path(routeHelper.prefixRoutes("/customers", "/**"))
                        .and()
                        .method(HttpMethod.GET)
                        .filters(fSpec -> fSpec.filter(securityHelper.authIfHasAnyRole(AccessPermissions.READ_CUST)))
                        .uri(apiProps.getUris().getCustomerServiceUri()))
                .route("customers-post-routes", r -> r.path(routeHelper.prefixRoutes("/customers", "/**"))
                        .and()
                        .method(HttpMethod.POST, HttpMethod.PATCH)
                        .filters(fSpec -> fSpec.filter(securityHelper.authIfHasAnyRole(AccessPermissions.WRITE_CUST)))
                        .uri(apiProps.getUris().getCustomerServiceUri()))
                .build();
    }
}
