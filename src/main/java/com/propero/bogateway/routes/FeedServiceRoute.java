package com.propero.bogateway.routes;

import com.propero.bogateway.config.ApiProps;
import com.propero.bogateway.helpers.RouteHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;

@Configuration(proxyBeanMethods = false)
@RequiredArgsConstructor
class FeedServiceRoute {

    private final RouteHelper routeHelper;
    private final ApiProps apiProps;

    @Bean
    RouteLocator feedRouteLocator(RouteLocatorBuilder builder) {
        return builder.routes()
                .route("feed-service_graphql-public", r -> r.path(routeHelper.prefixRoute("/public/graphql"))
                        .and()
                        .method(HttpMethod.GET)
                        .filters(filterSpec -> routeHelper.stripPrefix(filterSpec, 1))
                        .uri(apiProps.getUris().getFeedServiceUri()))
                .build();
    }

}
