package com.propero.bogateway.routes;

import com.propero.bogateway.config.AccessPermissions;
import com.propero.bogateway.elasticsearch.QueryParams;
import com.propero.bogateway.filters.factories.CustomerActivityGatewayFilterFactory;
import com.propero.bogateway.helpers.RouteHelper;
import com.propero.bogateway.helpers.SecurityHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Mono;

import java.util.Map;

import static java.lang.String.format;
import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.getUriTemplateVariables;

@Configuration
@RequiredArgsConstructor
public class CustomerActivityFeedRoute {

    private final SecurityHelper securityHelper;
    private final RouteHelper routeHelper;
    private final CustomerActivityGatewayFilterFactory customerActivityGatewayFilterFactory;

    private final String CUST_ID_VAR = "customerId";

    @Bean
    public RouteLocator customerActivityRoutes(RouteLocatorBuilder builder) {
        return builder.routes()
                .route("customer-activity-feed", r -> r.path(routeHelper.prefixRoute(format("/activity-feed/v1/customers/{%s}", CUST_ID_VAR)))
                        .and()
                        .method(HttpMethod.GET)
                        .filters(filterSpec -> filterSpec.filter(securityHelper.authIfHasAnyRole(AccessPermissions.READ_CUST))
                                .filter(customerActivityGatewayFilterFactory.apply(CustomerActivityGatewayFilterFactory.Config.from(
                                        exchange -> {
                                            Map<String, String> params = routeHelper.getRequestQueryParams(exchange);
                                            Map<String, String> uriTemplateVariables = getUriTemplateVariables(exchange);
                                            return Mono.just(defaultQueryParamsBuilder(params)
                                                    .customerId(extractCustomerId(uriTemplateVariables))
                                                    .documentType("ta")  // trading activity
                                                    .build());
                                        })))
                                //amending headers for Elasticsearch
                                .removeRequestHeader(HttpHeaders.AUTHORIZATION)
                                .addRequestHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        )
                        .uri(customerActivityGatewayFilterFactory.getElasticsearchUri())
                )
                .build();
    }

    private QueryParams.QueryParamsBuilder defaultQueryParamsBuilder(Map<String, String> queryParams) {
        try {
            return QueryParams.builder()
                    .from(QueryParams.getRequiredAsLong("from", queryParams))
                    .to(QueryParams.getRequiredAsLong("to", queryParams))
                    .offset(QueryParams.getAsLong("offset", queryParams, 0L))
                    .size(QueryParams.getAsLong("size", queryParams, 20L));
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Non-numeric query param(s) provided", e);
        }
    }

    private String extractCustomerId(Map<String, String> uriTemplateVariables) {
        return uriTemplateVariables.get(CUST_ID_VAR);
    }

}
