package com.propero.bogateway.docs;

import io.swagger.v3.core.converter.ModelConverters;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.security.OAuthFlow;
import io.swagger.v3.oas.annotations.security.OAuthFlows;
import io.swagger.v3.oas.annotations.security.OAuthScope;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.Operation;
import io.swagger.v3.oas.models.PathItem;
import io.swagger.v3.oas.models.Paths;
import io.swagger.v3.oas.models.media.Content;
import io.swagger.v3.oas.models.media.MediaType;
import io.swagger.v3.oas.models.media.Schema;
import io.swagger.v3.oas.models.parameters.Parameter;
import io.swagger.v3.oas.models.parameters.RequestBody;
import io.swagger.v3.oas.models.responses.ApiResponse;
import io.swagger.v3.oas.models.responses.ApiResponses;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springframework.cloud.gateway.route.Route;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.util.UriComponentsBuilder;
import org.springframework.web.util.UriTemplate;
import reactor.core.publisher.Flux;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Stream;

@Slf4j
@Configuration
@OpenAPIDefinition(
        info = @Info(
                title = "BackOffice Gateway",
                description = "Gateway securing Back Office",
                version = "v1"))
@SecurityScheme(
        name = "session-cookie",
        type = SecuritySchemeType.APIKEY,
        in = SecuritySchemeIn.COOKIE,
        paramName = "SESSION"
)
@SecurityScheme(
        name = "bearer-token",
        type = SecuritySchemeType.OAUTH2,
        flows = @OAuthFlows(authorizationCode = @OAuthFlow(
                authorizationUrl = "${springdoc.oauth-flow.authorizationUrl}",
                tokenUrl = "${springdoc.oauth-flow.tokenUrl}",
                scopes = {
                        @OAuthScope(name = "openid", description = "OpenId Connect")
                })))
public class OpenApiConfiguration {

    public static final String API_DOCS ="openapi-operation";
    public static final String API_SCHEMAS ="openapi-schemas";

    private static final Pattern pathsPattern = Pattern.compile("Paths: \\[(.*)],");
    private static final Pattern methodsPattern = Pattern.compile("Methods: \\[([^]]+)]");
    private static final Collection<PathItem.HttpMethod> modifyingMethods = Set.of(
            PathItem.HttpMethod.PUT, PathItem.HttpMethod.POST, PathItem.HttpMethod.PATCH);

    @Bean
    public OpenApiCustomizer openApiCustomizer(List<RouteLocator> locators) {
        List<Route> routeStream = locators.stream()
                .map(RouteLocator::getRoutes)
                .flatMap(Flux::toStream)
                .toList();
        return openApi -> {
            Paths apiPaths = openApi.getPaths();
            for (Route route : routeStream) {

                List<Class<?>> classes = (List<Class<?>>) route.getMetadata().getOrDefault(API_SCHEMAS, List.of());
                registerSchemas(openApi, classes);

                String predicatesAsString = route.getPredicate().toString();
                List<String> paths = parse(predicatesAsString, pathsPattern).toList();
                if (paths.isEmpty()) continue;

                String path = paths.getFirst().replaceAll("\\{(.*):.*?}","{$1}");

                List<PathItem.HttpMethod> methods = parse(predicatesAsString, methodsPattern).map(PathItem.HttpMethod::valueOf).toList();
                for (PathItem.HttpMethod method : methods) {
                    Operation docs = (Operation) route.getMetadata().getOrDefault(API_DOCS, new Operation());
                    Operation operation = new Operation();
                    operation.setDescription(docs.getDescription());
                    operation.setTags(docs.getTags());
                    operation.addSecurityItem(new SecurityRequirement().addList("bearer-token"));
                    operation.addSecurityItem(new SecurityRequirement().addList("session-cookie"));
                    operation.setResponses(docs.getResponses());
                    if (operation.getResponses() == null) {
                        operation.setResponses(new ApiResponses().addApiResponse("default", new ApiResponse().content(withDefaultJsonBody())));
                    }
                    operation.setRequestBody(docs.getRequestBody());
                    if (modifyingMethods.contains(method) && operation.getRequestBody() == null) {
                        operation.setRequestBody(new RequestBody().content(withDefaultJsonBody()));
                    }

                    Map<String,List<String>> queryParameters = UriComponentsBuilder.fromUriString(path).build().getQueryParams();
                    queryParameters.forEach((key, value) -> operation.addParametersItem(
                            new Parameter().name(key).example(path).in(ParameterIn.QUERY.toString())));

                    List<String> pathVariables = new UriTemplate(path).getVariableNames();
                    pathVariables.forEach(variable -> operation.addParametersItem(
                            new Parameter().required(true).name(variable).in(ParameterIn.PATH.toString())));

                    PathItem pathItem = Optional.ofNullable(apiPaths.get(path)).orElseGet(PathItem::new);
                    switch (method) {
                        case GET: pathItem.get(operation); break;
                        case PUT: pathItem.put(operation); break;
                        case POST: pathItem.post(operation); break;
                        case PATCH: pathItem.patch(operation); break;
                        case DELETE: pathItem.delete(operation); break;
                        case HEAD: pathItem.head(operation); break;
                        case TRACE: pathItem.trace(operation); break;
                        case OPTIONS: pathItem.options(operation); break;
                    }
                    openApi.path(path, pathItem);
                }
            }
        };
    }

    private static Stream<String> parse(String predicates, Pattern pattern) {
        Matcher matcher = pattern.matcher(predicates);
        if (matcher.find()) {
            return Stream.of(matcher.group(1).split(","))
                    .map(StringUtils::trim)
                    .map(p -> p.replaceAll("\\*", ""));
        } else {
            return Stream.empty();
        }
    }

    private static Content withDefaultJsonBody() {
        return new Content()
                .addMediaType("application/json", new MediaType().schema(new Schema<>().properties(Map.of())));
    }

    private static void registerSchemas(OpenAPI openApi, List<Class<?>> classes) {
        for (Class<?> clazz : classes) {
            ModelConverters.getInstance().readAllAsResolvedSchema(clazz).referencedSchemas
                    .forEach((name, schema) -> openApi.getComponents().addSchemas(name, schema));
        }
    }

    public static Content getDocumentContent(String name, Class<?> dto) {
        return new Content().addMediaType(name, new MediaType().schema(new Schema<>().$ref("#/components/schemas/" + dto.getSimpleName())));
    }
}
