package com.propero.bogateway.helpers;

import com.propero.bogateway.config.GatewayProperties;
import lombok.RequiredArgsConstructor;
import org.springframework.cloud.gateway.route.builder.GatewayFilterSpec;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;

import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;

@Component
@RequiredArgsConstructor
public class RouteHelper {

    private final GatewayProperties boGatewayProperties;

    /**
     * Prepare prefixed routes for spring-gateway PredicateSpec.
     *
     * @param prefix not the service path prefix, but particular route common prefix.
     * @param paths  individual routes handled by the same route definition.
     * @return prefixed (both service and controller's) routes
     */
    public String[] prefixRoutes(String prefix, String... paths) {
        return prefixRoute(Stream.of(paths)
                .map(path -> prefix + path)
                .toArray(String[]::new)
        );
    }

    public String[] prefixRoute(String... paths) {
        return Stream.of(paths)
                .map(p -> getApiPathPrefix() + p)
                .toArray(String[]::new);
    }

    private String getApiPathPrefix() {
//        return join("/", apiPathPrefixElements);
        return "";
    }

    public Map<String, String> getRequestQueryParams(ServerWebExchange exchange) {
        return exchange.getRequest().getQueryParams().toSingleValueMap();
    }

    public GatewayFilterSpec stripPrefix(GatewayFilterSpec fSpec, int partsToStrip) {
//        int numberOfPathsToStrip = apiPathPrefixElements.size() + partsToStrip;
        return fSpec.stripPrefix(partsToStrip);
    }

    public GatewayFilterSpec stripPrefix(GatewayFilterSpec fSpec) {
        return this.stripPrefix(fSpec, 0);
    }

    /**
     * Prepend the path prefix that is stripped by the Nginx proxy when routing external
     * requests to the gateway (default: {@code /bog}). If no prefix is configured, the
     * path is returned unaltered.
     */
    public String applyProxyPrefix(String path) {
        return Optional.ofNullable(boGatewayProperties.getProxyPathPrefix())
                .map(prefix -> prefix + path)
                .orElse(path);
    }
}
