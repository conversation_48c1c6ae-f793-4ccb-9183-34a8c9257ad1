package com.propero.bogateway.helpers;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.propero.bogateway.config.ApiProps;
import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DefaultDataBufferFactory;
import org.springframework.http.HttpMethod;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;

import java.net.URI;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class ElasticsearchHelper {
    private final ApiProps gatewayProperties;
    private final DataBufferFactory dataBufferFactory = new DefaultDataBufferFactory();
    private final ObjectMapper objectMapper;

    public URI buildElasticSearchFullUri(String indexPattern) {
        return URI.create(getElasticsearchUri() + '/' + indexPattern + "/_search?ignore_unavailable=true");
    }

    public String getElasticsearchUri() {
        return gatewayProperties.getUris().getElasticsearchUri();
    }

    public ServerWebExchange rebuildAsSearchRequest(ServerWebExchange exchange, DataBuffer searchRequestBody, String indexPattern) {
        return exchange.mutate()
                .request(new ServerHttpRequestDecorator(exchange.getRequest()) {
                    @Override
                    @Nonnull
                    public HttpMethod getMethod() {
                        return HttpMethod.POST;
                    }

                    @Override
                    @Nonnull
                    public Flux<DataBuffer> getBody() {
                        return Flux.just(searchRequestBody);
                    }

                    @Override
                    @Nonnull
                    public URI getURI() {
                        return buildElasticSearchFullUri(indexPattern);
                    }
                })
                .build();
    }

    @SneakyThrows
    public DataBuffer serializeSearchRequestBody(Map<String, Object> query) {
        return dataBufferFactory.wrap(objectMapper.writeValueAsBytes(query));
    }
}
