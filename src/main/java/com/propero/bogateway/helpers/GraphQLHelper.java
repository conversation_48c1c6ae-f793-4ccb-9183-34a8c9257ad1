package com.propero.bogateway.helpers;

import com.google.common.io.CharStreams;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.ParseContext;
import com.propero.bogateway.graphql.GraphQLQuery;
import com.propero.bogateway.routes.HasuraRoute;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
@RequiredArgsConstructor
public class GraphQLHelper {

    private static final Map<String,String> QUERY_CACHE = new ConcurrentHashMap<>();
    private static final String GRAPHQL_DOCUMENT_ROOT = "/graphql-documents/";

    private final ParseContext jsonPath;

    public <V> Mono<GraphQLQuery<V>> query(String name, V variables) {
        log.info("Executing GraphQL query {} with variables {}", name, variables);
        return Mono.just(GraphQLQuery.<V> builder()
                .operationName(name)
                .query(getQueryDocument(name))
                .variables(variables)
                .build());
    }

    public boolean pathExists(DocumentContext context, String path) {
        return context.read(path) != null;
    }

    public DocumentContext parse(Map<String, Object> data) {
        return jsonPath.parse(data);
    }

    private String getQueryDocument(String name) {
        return QUERY_CACHE.computeIfAbsent(name, key -> {
            Optional<InputStream> resourceAsStream = Optional.ofNullable(HasuraRoute.class.getResourceAsStream(GRAPHQL_DOCUMENT_ROOT + key + ".graphql"));
            if (resourceAsStream.isEmpty()) {
                throw new IllegalStateException("GraphQL document "+ key +" not found.");
            }
            try (Reader reader = new InputStreamReader(resourceAsStream.get())){
                return CharStreams.toString(reader);
            } catch (IOException e) {
                throw new IllegalStateException("Failed to read GraphQL document "+ key, e);
            }
        });
    }
}
