package com.propero.bogateway.elasticsearch;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;
import org.springframework.http.HttpStatus;
import org.springframework.web.server.ResponseStatusException;

import java.util.Map;
import java.util.Optional;

/**
 * Collates both path- and query parameters
 */
@Getter
@Builder(buildMethodName = "defaultBuild")
public class QueryParams {
    @Builder.Default
    private long offset = 0;
    @Builder.Default
    private long size = 20;
    @NonNull
    private final String customerId;
    @NonNull
    private final String documentType;
    private final long from;
    private final long to;

    public static Optional<Long> getAsLong(String key, Map<String, String> params) {
        return Optional.ofNullable(params.get(key)).map(Long::parseLong);
    }

    public static Long getAsLong(String key, Map<String, String> params, long defaultValue) {
        return getAsLong(key, params).orElse(defaultValue);
    }

    public static Long getRequiredAsLong(String key, Map<String, String> params) {
        return getAsLong(key, params).orElseThrow(() ->
                new ResponseStatusException(HttpStatus.BAD_REQUEST, String.format("[%s] param is missing", key))
        );
    }

    public static class QueryParamsBuilder {
        public QueryParams build() {
            try {
                return defaultBuild();
            } catch (Exception e) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, e.getMessage());
            }
        }
    }
}
