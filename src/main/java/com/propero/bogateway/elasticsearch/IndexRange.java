package com.propero.bogateway.elasticsearch;

import org.springframework.util.Assert;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import static java.lang.String.join;

/**
 * Represents the time range given Elasticsearch-related
 * query is made for.
 */
public class IndexRange {
    private static final DateTimeFormatter INDEX_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy.MM");

    private final LocalDate from;
    private final LocalDate to;

    public IndexRange(LocalDate from, LocalDate to) {
        Assert.isTrue(to.isEqual(from) || to.isAfter(from), "Date range is invalid");
        this.from = from;
        this.to = to;
    }

    public String getIndexPattern(AccountType accountType, String indexNamePrefix) {
        YearMonth from = toYearMonth(this.from);
        YearMonth to = toYearMonth(this.to);
        List<String> indices = new ArrayList<>();

        while (!from.isAfter(to)) {
            indices.add(join("-", indexNamePrefix, accountType.getIndexType(), from.format(INDEX_DATE_FORMATTER)));
            from = from.plusMonths(1L);
        }

        return join(",", indices);
    }

    private YearMonth toYearMonth(LocalDate date) {
        return YearMonth.of(date.getYear(), date.getMonth());
    }
}
