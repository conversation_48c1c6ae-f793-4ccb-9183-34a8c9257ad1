package com.propero.bogateway.config;

import io.micrometer.tracing.otel.bridge.Slf4JEventListener;
import io.opentelemetry.instrumentation.api.incubator.log.LoggingContextConstants;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class LoggingConfig {

    @Bean
    public Slf4JEventListener otelSlf4JEventListener() {
        return new Slf4JEventListener(LoggingContextConstants.TRACE_ID, LoggingContextConstants.SPAN_ID);
    }

}
