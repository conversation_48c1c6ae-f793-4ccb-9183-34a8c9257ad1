package com.propero.bogateway.config.security;

import com.propero.bogateway.filters.factories.AddUserDetailsFilterFactory;
import com.propero.bogateway.filters.factories.AuthRedirectGatewayFilterFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.oauth2.client.authentication.OAuth2AuthenticationToken;
import org.springframework.web.server.WebSession;

@Configuration
@RequiredArgsConstructor
public class OAuth2Routes {

    static final String PATH_PATTERN_USER_DETAILS = "/oauth2/user-details";
    static final String URI_NOT_ROUTED = "http://localhost:80";

    private final AddUserDetailsFilterFactory addUserDetailsFilterFactory;
    private final AuthRedirectGatewayFilterFactory authRedirectGatewayFilterFactory;

    /**
     * <PERSON>les requests to the {@code GET /oauth2/user-details} endpoint, once the OIDC
     * flow has completed and an {@link OAuth2AuthenticationToken} has been added
     * to the current {@link WebSession}.
     * <p>
     * User details are extracted from the claims and set into an exchange attribute which will
     * then be transformed in a query param.
     * The {@code redirect_uri} parameter is then used to redirect the caller
     * to a landing page which will handle the auth session.
     *
     * @see AddUserDetailsFilterFactory
     * @see AuthRedirectGatewayFilterFactory
     */
    @Bean
    RouteLocator oauth2Routes(RouteLocatorBuilder builder) {
        return builder.routes()
                .route("oauth2_route", r -> r.path(PATH_PATTERN_USER_DETAILS)
                        .and()
                        .method(HttpMethod.GET)
                        .filters(f -> f
                                .filter(addUserDetailsFilterFactory.apply())
                                .filter(authRedirectGatewayFilterFactory.apply())
                        )
                        .uri(URI_NOT_ROUTED))
                .build();
    }
}
