package com.propero.bogateway.config.security;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import java.util.Map;

@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@JsonDeserialize(using = LinkedTreeMapDeserializer.class)
class LinkedTreeMapMixin {

    @JsonCreator
    LinkedTreeMapMixin(Map<?, ?> map) {
    }

}