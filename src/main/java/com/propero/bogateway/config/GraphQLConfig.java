package com.propero.bogateway.config;

import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import com.jayway.jsonpath.ParseContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class GraphQLConfig {

    @Bean
    ParseContext jsonPath() {
        return JsonPath.using(com.jayway.jsonpath.Configuration.builder()
                .options(Option.DEFAULT_PATH_LEAF_TO_NULL)
                .build());
    }

}
