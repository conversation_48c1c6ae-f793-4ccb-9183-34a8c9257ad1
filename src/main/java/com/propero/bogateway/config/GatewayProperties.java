package com.propero.bogateway.config;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import java.time.Duration;

@Getter
@Setter
@Validated
@Component("boGatewayProperties")
@ConfigurationProperties(prefix = "bo-gateway")
public class GatewayProperties {

    @NotNull
    private String baseUri;

    @Pattern(regexp = "/.*[^/]", message = "Prefix must have leading slash and no trailing slash")
    private String proxyPathPrefix;

    @Valid
    private Security security = new Security();

    @Getter
    @Setter
    public static class Security {
        @Valid
        private Session session = new Session();
        @Valid
        private ConnectionPool connectionPool = new ConnectionPool();

    }

    @Getter
    @Setter
    public static class Session {
        @NotNull
        private Duration maxInactiveInterval;
    }

    @Getter
    @Setter
    public static class ConnectionPool {
        @NotNull
        private Duration maxLifeTime;
    }
}
