package com.propero.bogateway.config;

public final class AccessPermissions {
    public final static String READ_CUST = "view-customer";
    public final static String WRITE_CUST = "edit-customer";
    public final static String READ_KYC = "view-kyc";
    public final static String WRITE_KYC = "edit-kyc";
    public final static String DELETE_KYC = "delete-kyc";
    public final static String READ_TX = "view-transactions";
    public final static String WRITE_TX = "edit-transactions";
    public final static String WRITE_ACCOUNT = "create-account";
    public final static String VERIFY_ACCOUNT = "verify-account";
    public final static String WRITE_SUPPORT_COMMENT = "edit-comment-support";
    public final static String WRITE_COMPLIANCE_COMMENT = "edit-comment-compliance";
    public final static String CUSTOMER_RESET_PASSWORD = "reset-password";
}
