package com.propero.bogateway.config;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;


import static org.apache.commons.lang3.StringUtils.EMPTY;

@Data
@Validated
@Component
@ConfigurationProperties(prefix = "api")
public class ApiProps {

    @Valid
    private final ApiProps.SecurityProps security = new SecurityProps();
    @Valid
    private final ApiProps.TargetServiceProps uris = new TargetServiceProps();
    @Valid
    private final ApiProps.TimeoutProps timeouts = new TimeoutProps();

    @NotNull
    private String apiPathPrefix = EMPTY;

    @Getter
    @Setter
    public static class SecurityProps {
        private String rolePrefix = EMPTY;
    }

    @Getter
    @Setter
    public static class TargetServiceProps {
        @NotNull
        private String auditServiceUri;
        @NotNull
        private String customerServiceUri;
        @NotNull
        private String elasticsearchUri;
        @NotNull
        private String feedServiceUri;
        @NotNull
        private String kycServiceUri;
        @NotNull
        private String paymentServiceUri;
        @NotNull
        private String reportingServiceUri;
        @NotNull
        private String tradingAccountServiceUri;
        @NotNull
        private String tradingBalanceServiceUri;
        @NotNull
        private String hasuraServiceUri;
    }

    @Getter
    @Setter
    public static class TimeoutProps {
        @NotNull
        private int extendedSearchTimeout;
    }
}
