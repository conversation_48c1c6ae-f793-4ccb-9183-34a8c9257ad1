package com.propero.bogateway.config;

import com.propero.bogateway.helpers.RouteHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.security.web.server.savedrequest.WebSessionServerRequestCache;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

import java.net.URI;

/**
 * Extends the standard {@link WebSessionServerRequestCache} to account for the path
 * prefix that is stripped by <PERSON>inx on inbound requests (default: {@code /bog}).
 * <p>
 * Paths are stored and managed as normal. The prefix is simply applied to the path
 * on retrieval from the cache.
 */
@Component
@RequiredArgsConstructor
public class PrefixAwareRequestCache extends WebSessionServerRequestCache {

    private final RouteHelper routeHelper;

    @Override
    public Mono<URI> getRedirectUri(ServerWebExchange exchange) {
        return super.getRedirectUri(exchange)
                .map(uri -> UriComponentsBuilder.fromUri(uri)
                        .replacePath(routeHelper.applyProxyPrefix(uri.getPath()))
                        .build().toUri());
    }
}
