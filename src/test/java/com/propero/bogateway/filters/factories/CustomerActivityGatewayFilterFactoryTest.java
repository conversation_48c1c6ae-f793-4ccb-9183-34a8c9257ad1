package com.propero.bogateway.filters.factories;

import com.propero.bogateway.elasticsearch.QueryParams;
import com.propero.bogateway.filters.factories.CustomerActivityGatewayFilterFactory;
import com.propero.bogateway.filters.factories.CustomerActivityGatewayFilterFactory.Config;
import com.propero.bogateway.helpers.ElasticsearchHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.List;
import java.util.Map;

import static com.google.common.collect.ImmutableMap.of;
import static java.lang.Long.valueOf;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CustomerActivityGatewayFilterFactoryTest {

    @Mock
    private GatewayFilterChain filterChain;
    @Mock
    private DataBuffer dataBuffer;
    @Mock
    private ElasticsearchHelper elasticsearchHelper;
    @Captor
    private ArgumentCaptor<Map<String, Object>> queryParamCaptor;

    private CustomerActivityGatewayFilterFactory factory;

    @BeforeEach
    void setUp() {
        factory = new CustomerActivityGatewayFilterFactory(elasticsearchHelper);
        when(filterChain.filter(any())).thenReturn(Mono.empty());
    }

    @Test
    void okWhenLoggedInWithWhitelistedAuthorities() {
        // given
        String custId = "cust123";
        String from = "1636505675000";
        String to = "1636509675000";
        String offset = "120";
        String size = "500";
        String docType = "ta";
        ServerWebExchange req = MockServerWebExchange.from(MockServerHttpRequest.get("/bog/url-to-cust-activity-feed"));
        GatewayFilter filter = factory.apply(factoryConf(custId, from, to, offset, size, docType));
        when(elasticsearchHelper.serializeSearchRequestBody(queryParamCaptor.capture())).thenReturn(dataBuffer);

        // when
        Mono<Void> result = filter.filter(req, filterChain);

        // then
        StepVerifier.create(result).verifyComplete();
        verify(elasticsearchHelper).rebuildAsSearchRequest(eq(req), eq(dataBuffer), anyString());
        Map<String, Object> query = queryParamCaptor.getValue();
        verifyQueryParams(query, custId, from, to, offset, size, docType);
    }

    private Config factoryConf(String custId, String from, String to, String offset, String size, String docType) {
        QueryParams qp = QueryParams.builder()
                .customerId(custId)
                .from(valueOf(from))
                .to(valueOf(to))
                .offset(valueOf(offset))
                .size(valueOf(size))
                .documentType(docType)
                .build();

        return Config.from(r -> Mono.just(qp));
    }

    private void verifyQueryParams(Map<String, Object> actualQuery, String custId, String from, String to, String offset, String size, String docType) {
        List<Object> expectedMustParams = List.of(
                of("term", of("type", of("value", docType))),
                of("term", of("customerId", of("value", custId))),
                of("range", of("timestamp", of("gte", valueOf(from), "lt", valueOf(to)))));

        Map<String, Object> expectedQuery = of(
                "size", valueOf(size),
                "from", valueOf(offset),
                "sort", of("timestamp", "desc"),
                "query", of("bool", of("must", expectedMustParams))
        );

        assertEquals(expectedQuery, actualQuery);
    }
}
