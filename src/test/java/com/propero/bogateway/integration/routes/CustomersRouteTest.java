package com.propero.bogateway.integration.routes;

import com.c4_soft.springaddons.security.oauth2.test.annotations.OpenIdClaims;
import com.c4_soft.springaddons.security.oauth2.test.annotations.WithOAuth2Login;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.propero.bogateway.config.AccessPermissions;
import com.propero.bogateway.integration.BaseBoGatewayTest;
import lombok.Builder;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.assertj.core.api.Assertions.assertThat;

public class CustomersRouteTest extends BaseBoGatewayTest {

    @Test
    @WithOAuth2Login(authorities =  AccessPermissions.WRITE_SUPPORT_COMMENT,
            claims = @OpenIdClaims(preferredUsername = "just_a_test_user"))
    public void addSupportComment_userEmailIsAddedAsRequestHeader(){
        CustomerCommentRequest request = CustomerCommentRequest.builder()
                .comment("This is a test comment")
                .commentType("SUPPORT")
                .build();
        ResponseEntity<CustomerCommentResponse> response = testRestTemplate.postForEntity(getBaseUrl() + "/customers/1234/comments", request, CustomerCommentResponse.class);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        assertThat(response.getBody()).isNotNull();

        WireMock.verify(WireMock.postRequestedFor(WireMock.urlEqualTo("/customers/1234/comments"))
                .withHeader("x-bo_user_email", WireMock.equalTo("just_a_test_user")));
    }

    @Builder
    private record CustomerCommentRequest(String comment, String commentType) { }

    private record CustomerCommentResponse(long id,long customerId, String comment, String commentType, String createdByUser) { }
}
