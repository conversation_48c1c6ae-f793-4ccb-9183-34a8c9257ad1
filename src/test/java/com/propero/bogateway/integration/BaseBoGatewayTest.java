package com.propero.bogateway.integration;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.propero.bogateway.BoGatewayApplication;
import com.propero.bogateway.config.ApiProps;
import com.redis.testcontainers.RedisContainer;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.actuate.observability.AutoConfigureObservability;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalManagementPort;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.ApplicationContext;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.web.reactive.server.WebTestClient;
import org.testcontainers.containers.BindMode;
import org.testcontainers.containers.output.Slf4jLogConsumer;
import org.testcontainers.containers.wait.strategy.Wait;
import org.testcontainers.utility.DockerImageName;
import org.wiremock.integrations.testcontainers.WireMockContainer;

import java.time.Duration;
import java.time.temporal.ChronoUnit;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT, classes = BoGatewayApplication.class)
@ActiveProfiles(value = "it")
@AutoConfigureObservability
@Slf4j
public abstract class BaseBoGatewayTest {

    private static final Duration DEFAULT_CONTAINER_TIMEOUT = Duration.of(30, ChronoUnit.SECONDS);

    @Autowired
    protected ApiProps apiProps;
    @Autowired
    protected RetryTemplate retryTemplate;
    @Autowired
    protected TestRestTemplate testRestTemplate;

    @LocalManagementPort
    private Integer managementPort;

    @LocalServerPort
    private Integer serverPort;

    protected WebTestClient webTestClient;

    private static final WireMockContainer WIRE_MOCK_CONTAINER = new WireMockContainer(WireMockContainer.OFFICIAL_IMAGE_NAME)
            .withLogConsumer(new Slf4jLogConsumer(log))
            .withCliArg("--global-response-templating")
            .withClasspathResourceMapping("wiremock", "/home/<USER>/", BindMode.READ_ONLY)
            .withoutBanner();

    private static final RedisContainer REDIS_CONTAINER = new RedisContainer(DockerImageName.parse("redis:5.0.3-alpine"))
            .waitingFor(Wait.forSuccessfulCommand("redis-cli ping | grep PONG"))
            .withStartupTimeout(DEFAULT_CONTAINER_TIMEOUT)
            .withExposedPorts(6379);

    static {
        WIRE_MOCK_CONTAINER.start();
        REDIS_CONTAINER.start();
    }

    @DynamicPropertySource
    static void registerPgProperties(DynamicPropertyRegistry registry) {
        registry.add("api.uris.hasuraServiceUri", WIRE_MOCK_CONTAINER::getBaseUrl);
        registry.add("api.uris.customerServiceUri", WIRE_MOCK_CONTAINER::getBaseUrl);
        registry.add("REDIS_HOST", REDIS_CONTAINER::getHost);
        registry.add("REDIS_PORT", () -> REDIS_CONTAINER.getMappedPort(6379).toString());
        WireMock.configureFor(WIRE_MOCK_CONTAINER.getHost(), WIRE_MOCK_CONTAINER.getPort());
    }

    @BeforeEach
    void init(ApplicationContext context) {
        webTestClient = WebTestClient.bindToApplicationContext(context).build();
    }

    protected String getBaseUrl() {
        return "http://localhost:" + serverPort + "/" + apiProps.getApiPathPrefix();
    }

    protected String getManagementBaseUrl() {
        return "http://localhost:" + managementPort;
    }

}
