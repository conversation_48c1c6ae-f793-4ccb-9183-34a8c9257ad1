package com.propero.bogateway.integration;

import com.c4_soft.springaddons.security.oauth2.test.annotations.*;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.propero.bogateway.config.AccessPermissions;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
@ExtendWith(OutputCaptureExtension.class)
public class TracingFilterTest extends BaseBoGatewayTest {

    @Test
    @WithOAuth2Login(authorities = AccessPermissions.READ_CUST)
    public void verifyTraceparentAndBaggageHeadersPropagatedDownstream_sessionIdGetsGenerated(CapturedOutput output) {

        webTestClient.get().uri("/customers")
                .exchange()
                .expectStatus().isOk();

        // Verify WireMock received:
        // - a traceparent header following W3C format: version-traceid-parentid-traceflags
        // - a baggage header with sessionId
        WireMock.verify(getRequestedFor(urlEqualTo("/customers"))
                .withHeader("traceparent", matching("^00-[0-9a-f]{32}-[0-9a-f]{16}-00$"))
                .withHeader("baggage", matching("session_id=[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}")));

        // check all headers and traceId/spanId are logged
        Pattern pattern = Pattern.compile("\\[[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}] \\[[a-f0-9]{32}/[a-f0-9]{16}]");
        Matcher matcher = pattern.matcher(output.getOut());
        assertTrue(matcher.find());
    }

    @Test
    @WithOidcLogin(
            authorities = AccessPermissions.READ_CUST,
            claims = @OpenIdClaims(otherClaims = @Claims(stringClaims = {
                    @StringClaim(name = "sid", value = "1234567890")
            }))
    )
    public void verifyTraceparentAndBaggageHeadersPropagatedDownstream_sessionIdExtractedFromSidClaim(CapturedOutput output) {

        webTestClient.get().uri("/customers")
                .exchange()
                .expectStatus().isOk();

        // Verify WireMock received:
        // - a traceparent header following W3C format: version-traceid-parentid-traceflags
        // - a baggage header with sessionId
        WireMock.verify(getRequestedFor(urlEqualTo("/customers"))
                .withHeader("traceparent", matching("^00-[0-9a-f]{32}-[0-9a-f]{16}-00$"))
                .withHeader("baggage", matching("session_id=1234567890")));

        // check all headers and traceId/spanId are logged
        Pattern pattern = Pattern.compile("\\[1234567890] \\[[a-f0-9]{32}/[a-f0-9]{16}]");
        Matcher matcher = pattern.matcher(output.getOut());
        assertTrue(matcher.find());
    }
}
