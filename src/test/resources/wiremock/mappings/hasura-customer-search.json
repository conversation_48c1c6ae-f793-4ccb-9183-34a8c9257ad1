{"mappings": [{"priority": 1, "request": {"urlPattern": "/v1/graphql", "method": "POST", "bodyPatterns": [{"matchesJsonPath": {"expression": "$.operationName", "contains": "BackOfficeCustomerSearch"}}, {"matchesJsonPath": {"expression": "$.variables", "equalToJson": {"customerCondition": {"created_at": {"_gte": "2023-01-31T23:00:00", "_lte": "2023-02-27T23:00:00"}, "person": {"country": {"_eq": "ES"}}, "user": {"email_principal": {"_ilike": "%<EMAIL>%"}}}, "pageSize": 50, "pageOffset": 0}}}]}, "response": {"status": 200, "headers": {"Content-Type": "application/json;charset=UTF-8"}, "jsonBody": {"data": {"customer_db": {"stats": {"aggregate": {"count": 1}}, "customers": [{"id": 156359, "legalEntity": "CYSEC", "email": "<EMAIL>", "brand": "SKILLING", "registrationMethod": "MANUAL", "onboardingStatus": "VERIFICATION_COMPLETE", "currency": "EUR", "created": "2023-02-09T10:36:25.264", "kycSummary": {"identityVerificationProvider": "MANUAL", "identityVerificationStatus": "VERIFIED", "amlWatchlistProvider": "TRULIOO", "amlWatchlistStatus": "NO_HIT"}, "fullName": {"firstName": "Frida", "lastName": "<PERSON><PERSON><PERSON>"}, "tradingAccount": [{"real": false, "provider": "com.propero.tas.account.SpotwareTradingAccount", "created": "2024-05-17T08:12:08.313+00:00", "externalAccountId": "4412123"}], "deposits": {"aggregate": {"sum": {"amount": 4089.79}}}}]}}}, "transformers": ["response-template"]}}]}