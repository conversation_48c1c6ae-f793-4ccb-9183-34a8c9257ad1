{"mappings": [{"priority": 1, "request": {"urlPattern": "/v1/graphql", "method": "POST", "bodyPatterns": [{"matchesJsonPath": {"expression": "$.operationName", "contains": "BackOfficeTransactionsByCustomer"}}, {"matchesJsonPath": {"expression": "$.variables", "equalToJson": {"customerCondition": {"id": {"_eq": 13710}, "person": {"first_name": {"_like": "%John%"}, "last_name": {"_like": "%Doe%"}}}, "transactionCondition": {"demo": {"_eq": false}, "compensating": {"_eq": false}}, "size": 100, "offset": 0, "sortBy": "desc"}}}]}, "response": {"status": 200, "headers": {"Content-Type": "application/json;charset=UTF-8"}, "jsonBody": {"data": {"results": {"customers": [{"transactions": [{"customer": {"customerId": 13710, "email": "<EMAIL>", "person": {"firstName": "<PERSON>", "lastName": "<PERSON>"}}, "transactionId": "mt4-*********-***********-JezT-mt4-20384", "createdAt": "2023-04-19T14:33:11.309597+00:00", "updatedAt": null, "tradingAccountId": "mt4-*********-***********-JezT", "delta": 201.3, "transactionType": "CREDIT", "license": "CYSEC", "provider": "MT4", "providerTransactionType": "DEPOSIT", "paymentMethod": "Credit Card", "operationType": "DEPOSIT", "currency": "GBP", "description": "p1722561", "externalOperationId": "p1722561", "status": "FINISHED", "payment": {"destinationType": "PRAXIS", "paymentProcessor": "Test Card Processor", "comment": null}, "amount": -201.3, "amountEur": 228.64}]}]}}}, "transformers": ["response-template"]}}, {"priority": 1, "request": {"urlPattern": "/v1/graphql", "method": "POST", "bodyPatterns": [{"matchesJsonPath": {"expression": "$.operationName", "contains": "BackOfficeTransactionsByTransaction"}}, {"matchesJsonPath": {"expression": "$.variables", "equalToJson": {"transactionCondition": {"internal_transaction_id": {"_eq": "91112"}, "demo": {"_eq": false}, "compensating": {"_eq": false}}, "size": 100, "offset": 0, "sortBy": "desc"}}}]}, "response": {"status": 200, "headers": {"Content-Type": "application/json;charset=UTF-8"}, "jsonBody": {"data": {"results": {"transactions": [{"customer": {"customerId": 13710, "email": "<EMAIL>", "person": {"firstName": "<PERSON>", "lastName": "<PERSON>"}}, "transactionId": "mt4-*********-***********-JezT-mt4-20384", "createdAt": "2023-04-19T14:33:11.309597+00:00", "updatedAt": null, "tradingAccountId": "mt4-*********-***********-JezT", "delta": 201.3, "transactionType": "CREDIT", "license": "CYSEC", "provider": "MT4", "providerTransactionType": "DEPOSIT", "paymentMethod": "Credit Card", "operationType": "DEPOSIT", "currency": "GBP", "description": "p1722561", "externalOperationId": "p1722561", "status": "FINISHED", "payment": {"destinationType": "PRAXIS", "paymentProcessor": "Test Card Processor", "comment": null}, "amount": -201.3, "amountEur": 228.64}]}}}, "transformers": ["response-template"]}}, {"priority": 1, "request": {"urlPattern": "/v1/graphql", "method": "POST", "bodyPatterns": [{"matchesJsonPath": {"expression": "$.operationName", "contains": "BackOfficeTransactionsByTransaction"}}, {"matchesJsonPath": {"expression": "$.variables", "equalToJson": {"transactionCondition": {"internal_transaction_id": {"_eq": "empty"}, "demo": {"_eq": false}, "compensating": {"_eq": false}}, "size": 100, "offset": 0, "sortBy": "desc"}}}]}, "response": {"status": 200, "headers": {"Content-Type": "application/json;charset=UTF-8"}, "jsonBody": {"data": {"results": {"transactions": []}}}, "transformers": ["response-template"]}}, {"priority": 1, "request": {"urlPattern": "/v1/graphql", "method": "POST", "bodyPatterns": [{"matchesJsonPath": {"expression": "$.operationName", "contains": "BackOfficeTransactionsByTransaction"}}, {"matchesJsonPath": {"expression": "$.variables", "equalToJson": {"transactionCondition": {"transaction_date": {"_gte": "2023-05-20T14:00:00", "_lte": "2023-05-21T01:00:59.999"}, "updated_at": {"_gte": "2023-03-20T14:00:00", "_lte": "2023-03-21T01:00:59.999"}, "demo": {"_eq": false}, "compensating": {"_eq": false}}, "size": 100, "offset": 0, "sortBy": "desc"}}}]}, "response": {"status": 200, "headers": {"Content-Type": "application/json;charset=UTF-8"}, "jsonBody": {"data": {"results": {"transactions": [{"customer": {"customerId": 13710, "email": "<EMAIL>", "brand": "SKILLING", "person": {"firstName": "<PERSON>", "lastName": "<PERSON>"}}, "transactionId": "mt4-*********-***********-JezT-mt4-20384", "createdAt": "2023-04-19T14:33:11.309597+00:00", "updatedAt": null, "tradingAccountId": "mt4-*********-***********-JezT", "delta": 201.3, "transactionType": "CREDIT", "license": "CYSEC", "provider": "MT4", "providerTransactionType": "DEPOSIT", "paymentMethod": "Credit Card", "operationType": "DEPOSIT", "currency": "GBP", "description": "p1722561", "externalOperationId": "p1722561", "status": "FINISHED", "payment": {"destinationType": "PRAXIS", "paymentProcessor": "Test Card Processor", "comment": null}, "amount": -201.3, "amountEur": 228.64}]}}}, "transformers": ["response-template"]}}]}