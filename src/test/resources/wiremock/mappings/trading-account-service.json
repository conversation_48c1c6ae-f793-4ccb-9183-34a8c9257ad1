{"mappings": [{"request": {"urlPattern": "/v1/customers/[0-9]+", "method": "GET"}, "response": {"status": 200, "headers": {"Content-Type": "application/json;charset=UTF-8"}, "jsonBody": {"customerId": "{{request.requestLine.pathSegments.[2]}}", "email": "<EMAIL>", "currency": "EUR", "license": "CYSEC", "active": true}, "transformers": ["response-template"]}}, {"request": {"urlPattern": "/v1/customers/[0-9]+/accounts/.+\\?idType=EXTERNAL", "method": "GET"}, "response": {"status": 200, "headers": {"Content-Type": "application/json;charset=UTF-8"}, "jsonBody": {"id": "{{randomValue length=8 type='ALPHANUMERIC'}}", "demo": false}, "transformers": ["response-template"]}}]}