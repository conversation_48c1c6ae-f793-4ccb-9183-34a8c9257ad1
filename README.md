# BO Gateway
Service proxying back-office-related api calls to the appropriate backend services.

It applies a security filter based on [permissions](src/main/java/com/propero/bogateway/config/AccessPermissions.java)
present in the WebSession. Also applies URL rewriting if needed.

Redirections are made to:
- audit-service
- customer-service
- feed-service
- hasura
- kyc-gateway
- payment-service
- reporting-service
- trading-account-service
- trading-balance-service

## Tech Stack
- Java 21
- Spring Boot
- Spring Cloud Gateway
- Spring Cloud Security

## Other interactions
This service invokes `ElasticSearch` to get activity feed for customers.

## Endpoints
A swagger-like list of supported routes and endpoints is available at the following urls:
- [Development](http://bo-gateway.staging.internal:201/swagger-ui.html ) (with dev VPN on)
- [Production](http://bo-gateway:201/swagger-ui.html) (with prod VPN on)
- [Local](http://localhost:3000/swagger-ui.html) (see [local run](#local-run) section)

## Local run

The application can be run with Spring Boot, either from the command line or within Intellij.

### Dependencies

At minimum, <PERSON><PERSON> and <PERSON><PERSON><PERSON> are required to run the gateway and access the API at `http://local.skilling.com/bog`.
Dependencies can be run via Docker using `docker-compose --profile local up` from the main folder of the project,
or `docker-compose up` if we want to let our project point to Staging.

In case of running KC locally, we might want to define some environment variables before starting the application,
the following are the most important:
 - `KEYCLOAK_CLIENT_ID=<client-id-defined-in-KC>`
 - `KEYCLOAK_CLIENT_SECRET=<secret-of-the-KC-client>;`
 - `KEYCLOAK_REALM=<name-of-the-realm>;`
 - `KEYCLOAK_URL=<"http://localhost:8080" if using Docker>`

## Security
Users must be authenticated to be properly proxied to the backend services.

The authentication role is played by Keycloak, which integrates with Google (IPD) to authenticate the user and
generate a JWT which is stored in a session.  
  
To make this work, each user must have groups (and indirectly) permissions assigned on Keycloak.

## Keycloak configuration
For details about how to configure a realm, client, Identity Provider and permissions, please refer to 
[KC-README](./doc/KC-README.md)