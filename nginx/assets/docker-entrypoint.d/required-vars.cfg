# AWS_IP from /etc/sysconfig/instance_info mounted from host machine
AWS_IP
BACKEND_HEALTHCHECK_PATH
# BACKEND_PORT used to generate proxy pass path e.g. http://{{ENV.AWS_IP}}:{{ENV.BACKEND_PORT}}
BACKEND_PORT
DNS_RESOLVER
GZIP_MIN_LENGTH
KEEPALIVE_TIMEOUT
LISTEN_PORT
# NGINX_ENVSUBST_OUTPUT_DIR set in Dockerfile. Override via docker run -e
NGINX_ENVSUBST_OUTPUT_DIR
# PLATFORM: skilling-trader | tradingmoon
PLATFORM
PUBLIC_PATH_PREFIX
# SITE_HOST: host header value in the original public request
SITE_HOST
# SITE_PROTOCOL: http|https in the original public request
SITE_PROTOCOL
# STACK: stag | prod
STACK
