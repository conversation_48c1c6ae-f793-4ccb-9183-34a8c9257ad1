# vim: et sr sw=2 ts=2 smartindent:
# NAT GW IPs (what <PERSON><PERSON><PERSON><PERSON> will see as client ip for requests via our webproxy
webproxy_ips:
  stag:
    - ***********
    - ************
  prod:
    - ***********;
    - ************;

# used in map to specify valid CORs domains
map_items_cors:
  # other: allows any origin: suitable for any non-prod, private stack
  other: |
    default "yes";

  bo:
    stag: |
      default "no";
      "~*^https://.*\.skilling\.com$" "yes";
      "~*^http://.*\.skilling\.com$"  "yes";

    prod: |
      default "no";
      "~*^https://.*\.skilling\.com$" "yes";
      "~*^http://.*\.skilling\.com$"  "yes";
# vim: et sr sw=2 ts=2 smartindent:
