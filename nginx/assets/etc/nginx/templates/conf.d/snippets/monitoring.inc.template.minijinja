{% set backend_uri = "http://" ~ ENV.AWS_IP ~ ":" ~ ENV.BACKEND_PORT -%}
# usage: <in server block>
# In http block outside of server block, include a map that sets $internal_req.
# Default map at /etc/nginx/conf.d/maps/internal-req.inc assumes anything without
# cloudflare ip header is internal, as the network rules enforce that external
# requests must go via cloudflare.
# Then, include this snippet in your server block:
#
#   include /etc/nginx/conf.d/snippets/monitoring.inc;
#
# ENV VARS:
#   AWS_IP
#   BACKEND_PORT
#   BACKEND_HEALTHCHECK_PATH
location = /self/health {
    if ($internal_req = "no") {
        return 404;
    }

    proxy_pass {{backend_uri}}{{ENV.BACKEND_HEALTHCHECK_PATH}} ;
    include /etc/nginx/conf.d/snippets/proxy-headers.inc ;

    add_header Cache-control $cc_val;
}

location = /self/status {
    if ($internal_req = "no") {
        return 404;
    }
    stub_status;
    access_log   off;
    server_tokens on;
}
# vim: et sr sw=4 ts=4 smartindent syntax=nginx:
# ... syntax plugin @ https://github.com/chr4/nginx.vim
