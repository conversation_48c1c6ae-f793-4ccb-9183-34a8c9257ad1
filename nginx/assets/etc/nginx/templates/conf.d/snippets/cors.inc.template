if ($request_method = "OPTIONS") {
    add_header Access-Control-Allow-Origin $cors_origin;
    add_header Access-Control-Allow-Credentials true;
    add_header Access-Control-Allow-Methods *;
    add_header Access-Control-Allow-Headers *;
    return 204;
}

add_header Access-Control-Allow-Origin $cors_origin always;
add_header Access-Control-Allow-Credentials true;
add_header Access-Control-Allow-Headers *;
add_header Access-Control-Expose-Headers *;
# vim: et sr sw=4 ts=4 smartindent syntax=nginx:
# ... syntax plugin @ https://github.com/chr4/nginx.vim
