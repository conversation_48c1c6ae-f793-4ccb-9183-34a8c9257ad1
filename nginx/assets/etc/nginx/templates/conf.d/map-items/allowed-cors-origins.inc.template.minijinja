# map defined in conf.d/maps/allowed-cors-origins.inc.template
# ENV VARS:
# PLATFORM == {{ ENV.PLATFORM }}
# STACK    == {{ ENV.STACK }}
# If map_items_cors.<PLATFORM>.<STACK> exists will paste it in this template.
{% set m = map_items_cors -%}
{% set cfg = m["other"] -%}
{% if m[ENV.PLATFORM] is defined and m[ENV.PLATFORM] is not none -%}
    # PLATFORM set to {{ENV.PLATFORM}}

    {% set p = m[ENV.PLATFORM] -%}
    {% if p[ENV.STACK] is defined and p[ENV.STACK] is not none -%}
        # STACK set to {{ENV.STACK}}
        {% set cfg = p[ENV.STACK] -%}
    {% endif -%}

{% endif -%}
{{ cfg }}
# vim: et sr sw=4 ts=4 smartindent syntax=nginx:
# ... syntax plugin @ https://github.com/chr4/nginx.vim
