# VARS:
#   $AWS_IP
#   $BACKEND_HEALTHCHECK_PATH - from monitoring.inc
#   $BACKEND_PORT
#   $DNS_RESOLVER - ******** for AWS, 127.0.0.11 for local docker
#   $GZIP_MIN_LENGTH - from proxy-base conf.d/snippets/gzip.inc
#   $KEEPALIVE_TIMEOUT - from proxy-base nginx.conf
#   $LISTEN_PORT - docker container port
#   $PLATFORM - bo - from map-items/allowed-cors-origins.inc
#   $PUBLIC_PATH_PREFIX - also used in proxy-base conf.d/snippets/springboot-public-route-blocks.inc
#   $SITE_HOST - expected host name for public requests
#   $SITE_PROTOCOL - expected protocol for public requests
#   $STACK - stag|prod - from map-items/allowed-cors-origins.inc
{% set backend_uri = "http://" ~ ENV.AWS_IP ~ ":" ~ ENV.BACKEND_PORT -%}
{# account for leading/trailing slashes in public path prefix #}
{% set prefix = ENV.PUBLIC_PATH_PREFIX -%}
{% set prefix = prefix[1:] if prefix | first == '/' else prefix -%}
{% set prefix = prefix[0:-1] if prefix | last == '/' else prefix -%}
# MAPS =>
include /etc/nginx/conf.d/maps/allowed-cors-origins.inc;
include /etc/nginx/conf.d/maps/cache-control.inc;
include /etc/nginx/conf.d/maps/client-ip.inc;
include /etc/nginx/conf.d/maps/internal-req.inc;
include /etc/nginx/conf.d/maps/normalise-log-time-fields.inc;
include /etc/nginx/conf.d/maps/proxy-fwd-headers.inc;
# <= MAPS

server {
    proxy_busy_buffers_size 24k;
    proxy_buffer_size 16k;
    proxy_buffers 64 4k;

    sendfile  off;

    listen {{ENV.LISTEN_PORT}};
    resolver {{ENV.DNS_RESOLVER}} valid=300s;
    server_name {{ENV.SITE_HOST}};

    set_real_ip_from 0.0.0.0/0;
    real_ip_recursive on;
    real_ip_header X-Forwarded-For;

    # BLOCKED =>
    include /etc/nginx/conf.d/snippets/springboot-public-route-blocks.inc;
    # <= BLOCKED

    # DEFAULT
    location /{{prefix}}/ {
        include /etc/nginx/conf.d/snippets/cors.inc;
        include /etc/nginx/conf.d/snippets/gzip.inc;

        client_max_body_size 5m;

        proxy_pass {{backend_uri}}/ ;
        include /etc/nginx/conf.d/snippets/proxy-headers.inc;

        add_header Cache-control $cc_val;
    }

    # /self/health,/self/status =>
    include /etc/nginx/conf.d/snippets/monitoring.inc;
    # <= /self/health,/self/status
}
# vim: et sr sw=4 ts=4 smartindent syntax=nginx:
# ... syntax plugin @ https://github.com/chr4/nginx.vim
