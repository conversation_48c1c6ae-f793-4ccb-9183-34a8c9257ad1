# ENV VARS:
#   SITE_PROTOCOL   - http/https in original request of end-user
#   SITE_HOST       - hostname header value from original request of end-user

### cors: allowed origins =>
# outputs: $cors_origin, based on origin header or configured site base url
#   If origin header value not in the map-items, the cors_origin in the Allow header will be set to this
#   service's expected public site protocol://host.
#   This effectively rejects the client request as this can never be the Origin sent by your client -
#   clients don't send CORs headers for XHR requests to the same protocol://host as their origin

map $http_origin $allowed_cors_origin {
    include /etc/nginx/conf.d/map-items/allowed-cors-origins.inc;
}

map $allowed_cors_origin $cors_origin {
    default "${SITE_PROTOCOL}://${SITE_HOST}";
    "yes" $http_origin;
}

### <= cors: allowed origins
# vim: et sr sw=4 ts=4 smartindent syntax=nginx:
# ... syntax plugin @ https://github.com/chr4/nginx.vim
