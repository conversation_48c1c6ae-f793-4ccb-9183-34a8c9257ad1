# nginx

> Public proxy that is tightly coupled to bo-gateway.
> * blocks admin routes from bo users
>   (but still available from internal network)
> * ensures expected proxy headers passed on
> * provides default Cache-Control header
> * gzips responses
> Is built for, and deployed onto AWS ECS stacks (stag|prod).

# TODO
* local testing docker-compose file and .dockerenv to ensure startup
    * to be used in integration test below

* basic test to verify there will be no minijinja rendering errors - can just
    run scripts on each variation of STACK/PLATFORM and check for any rendering errors
    * would invoke like:

```bash
# on failure would exit 1, so can just check for exit code
docker run --rm test-minijinja -t \
    -e PLATFORM -e STACK \
    --env-file defaults.dockerenv \
    --entrypoint /docker-entrypoint.d/02-render-minijinja-templates.sh
        propero:bo-gateway-proxy:candidate
```

* Integration tests on build of nginx.
    Need to migrate the relevant skilling-com-proxy BATS tests.

# CONFIG

## Files under etc/nginx/templates

Files ending in `.minijinja` are processed by `minijinja` and output in the same location
without the .minijinja suffix.

Files ending `.template` are processed by `envsubst` (env vars tokens of form ${..} are replaced.
The output filepath is the same without the /templates/ part in the path, and with the .template part stripped.

e.g.
On startup scripts under /docker-entrypoint.d are run:

```
/docker-entrypoint.d/02-render-minijinja-templates.sh
    renders etc/nginx/templates/conf.d/maps/foo.inc.template.minijinja
    outputs etc/nginx/templates/conf.d/maps/foo.inc.template
```

Later during startup:

```
/docker-entrypoint.d/20-envsubst-on-templates.sh
    renders etc/nginx/templates/conf.d/maps/foo.inc.template
    outputs etc/nginx/conf.d/maps/foo.inc
```

To use the rendered file in your nginx config you would:
```
# in default.conf or other file
include /etc/nginx/conf.d/maps/foo.inc;
```

## PROXY BACKEND URI

Although the nginx and bo-gateway both run as containers within the
same task, they can not easily communicate with each other unless running
in Fargate or using AWS VPC networking mode.

Although the two containers share a docker network, on EC2 they can not address each other
via any of the standard docker DNS methods (except by using the long-deprecated --link option).

None of those options are suitable.

Therefore our nginx makes requests to the host ec2 instance's private IP,
to an exposed port that is mapped to the bo-gateway container's listening port.

So the nginx will route requests via the host EC2 instance's private address as we know this IP
will remain the same throughout the container's life.

The bo-gateway container always exposes the same fixed host port.

### How does the nginx know the host IP and port number?

Our EC2 hosts all share a startup job that records the host ip in `/etc/sysconfig/instance_info`
under the env var name `AWS_IP`.

This file is mounted on the nginx container at the same path.

The minijinja startup script sources this file making all consumed env vars available
including `AWS_IP`, during rendering.

If the token `{{ENV.AWS_IP}}` is present in any .minijinja files, it will get replaced by the host
private IP when `02-render-minijinja-templates.sh` runs.

## BUILD

Manual steps for nginx build

```bash
docker pull propero/proxy-base:stable # get latest stable image
cd nginx
VERSION_TIMESTAMP=$(date +'%Y-%m-%d_%H%M%S')
DOCKER_IMAGE="propero/bo-gateway-proxy:candidate-$VERSION_TIMESTAMP"
docker buildx inspect --bootstrap

docker buildx create \
    --use \
    --platform=linux/arm64,linux/amd64 \
    --name multi-platform-builder

docker buildx build \
    --push --platform linux/amd64,linux/arm64 \
    $DOCKER_IMAGE -f Dockerfile .

echo "built $DOCKER_IMAGE"
```

