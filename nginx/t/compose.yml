# vim: et sr sw=2 ts=2 smartindent syntax=yaml:
name: bogw

x-upstream: &upstream
  image: jmalloc/echo-server:0.3.6
  environment:
    LOG_HTTP_BODY: true
    LOG_HTTP_HEADERS: true
    PORT: 8080
  ports:
    - "8080:8080"
  networks:
    apps: {}

services:
  bo-gateway-proxy:
    image: propero/bo-gateway-proxy:candidate
    ports:
      - "80:80"
    networks:
      - apps
    env_file:
      - compose.env
    depends_on:
      - bo-gateway
    healthcheck:
      test: /self/health
      interval: 10s
      retries: 5
      start_period: 10s
      timeout: 10s

  bo-gateway:
    hostname: bo-gateway
    <<: *upstream

networks:
  apps:
    driver: bridge
    ipam:
      config:
        - subnet: **********/24
          gateway: **********

