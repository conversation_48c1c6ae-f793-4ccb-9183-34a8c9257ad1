version: '3'

services:
  redis:
    image: redis:5.0.8
    ports:
      - "6379:6379"
    healthcheck:
      test: [ "CMD-SHELL", "redis-cli ping | grep PONG" ]
      interval: 2s
      timeout: 2s
      retries: 10

  nginx:
    profiles: [local]
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./local/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    extra_hosts:
      - "host.docker.internal:host-gateway"

  bo-keycloak_web:
    profiles: [local]
    image: quay.io/keycloak/keycloak:26.0.7
    command: "--verbose start-dev"
    environment:
      KC_DB: postgres
      KC_DB_URL: *********************************************
      KC_DB_USERNAME: keycloak
      KC_DB_PASSWORD: password

      KC_HOSTNAME: localhost
      KC_HOSTNAME_PORT: 8080
      KC_HOSTNAME_STRICT: "false"
      KC_HOSTNAME_STRICT_HTTPS: "false"

      KC_LOG_LEVEL: info
      KC_METRICS_ENABLED: "true"
      KC_HEALTH_ENABLED: "true"
      KC_FEATURES: "account:v3"

      KC_BOOTSTRAP_ADMIN_USERNAME: admin
      KC_BOOTSTRAP_ADMIN_PASSWORD: admin
      #DEBUG_SUSPEND: y
      #DEBUG_PORT: "*:8787"
    depends_on:
      - bo-keycloakdb
    ports:
      - 8080:8080
      - 8787:8787

  bo-keycloakdb:
    profiles: [local]
    image: postgres:15
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      POSTGRES_DB: keycloak
      POSTGRES_USER: keycloak
      POSTGRES_PASSWORD: password
    ports:
      - 5432:5432

volumes:
  postgres_data: